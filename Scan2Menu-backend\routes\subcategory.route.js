const express = require('express');
const router = express.Router();
const subcategoryController = require('../controllers/subcategory.controller');


// Add a new subcategory
router.post('/', subcategoryController.addSubcategory);

// Get a single subcategory by ID
router.get('/s/:id', subcategoryController.getSingleSubcategory);

// Get all subcategories of a category by category ID
router.get('/:categoryId', subcategoryController.getAllSubcategoriesByCategory);

// Update a subcategory by ID
router.put('/:id', subcategoryController.updateSubcategory);

// Delete a subcategory by ID
router.delete('/:id', subcategoryController.deleteSubcategory);

// Reorder subcategories within a category for drag and drop
router.put('/reorder/:categoryId', subcategoryController.reorderSubcategories);

module.exports = router;
