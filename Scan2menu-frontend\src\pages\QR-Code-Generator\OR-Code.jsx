// ORCode.jsx
import React, { useState, useEffect, useContext } from 'react';
import QRCode from 'react-qr-code';
import { RestaurantDataContext } from '../../context/RestaurantContext';
import { toast } from 'react-hot-toast';
import { getQRCodeSettings, updateQRCodeSettings } from '../../api'; // import our new API functions

const ORCode = () => {
  const { restaurantData } = useContext(RestaurantDataContext);

  // Initial defaults; note that qrLink is computed from restaurantData.slug later
  const [qrOptions, setQrOptions] = useState({
    foregroundColor: '#000000',
    backgroundColor: '#FFFFFF',
    padding: 5,
    cornerRadius: 50,
    qrLink: '', // will be set based on restaurantData
    displayText: 'PATEL CAFE',
    textColor: '#00FF00',
    textSize: 16,
  });

  const [savedOptions, setSavedOptions] = useState(null);

  // When restaurantData is available, set the qrLink and fetch settings from DB.
  useEffect(() => {
    if (restaurantData) {
      // Compute the qrLink based on environment and restaurant slug
      const computedQrLink =
        import.meta.env.VITE_ENV === 'development'
          ? `http://192.168.245.64:5173/restaurant/${restaurantData.slug}`
          : `https://scan-2-menu.netlify.app/restaurant/${restaurantData.slug}`;
      
      // Update state with the computed qrLink
      setQrOptions(prev => ({ ...prev, qrLink: computedQrLink }));

      // Fetch saved settings from the database using restaurantData._id or restaurantData.id
      // (Make sure your restaurantData object contains the proper identifier.)
      getQRCodeSettings(restaurantData._id)
        .then(response => {
          if (response.data && response.data.success) {
            const savedSettings = response.data.data.qrCodeSettings;
            // Merge saved settings with computed qrLink (if needed)
            setQrOptions(prev => ({
              ...prev,
              ...savedSettings,
              qrLink: computedQrLink,
            }));
            setSavedOptions(savedSettings);
          }
        })
        .catch(error => {
          console.error('Error fetching QR Code settings:', error);
          toast.error('Failed to fetch QR Code settings.');
        });
    }
  }, [restaurantData]);

  const handleOptionChange = (key, value) => {
    // Prevent changing qrLink from the UI
    if (key === 'qrLink') return;

    setQrOptions(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSave = () => {
    // Prepare settings to save (exclude qrLink since it's computed)
    const settingsToSave = {
      displayText: qrOptions.displayText,
      foregroundColor: qrOptions.foregroundColor,
      backgroundColor: qrOptions.backgroundColor,
      textColor: qrOptions.textColor,
      textSize: qrOptions.textSize,
      padding: qrOptions.padding,
      cornerRadius: qrOptions.cornerRadius,
    };

    updateQRCodeSettings(restaurantData._id, settingsToSave)
      .then(response => {
        if (response.data && response.data.success) {
          setSavedOptions(settingsToSave);
          toast.success('QR Code settings saved successfully!');
        }
      })
      .catch(error => {
        console.error('Error saving QR Code settings:', error);
        toast.error('Failed to save QR Code settings.');
      });
  };

  const handleReset = () => {
    if (savedOptions) {
      setQrOptions(prev => ({
        ...prev,
        ...savedOptions,
      }));
      toast.info('Changes reset to last saved settings');
    }
  };

  const downloadQR = () => {
    const svg = document.getElementById('qr-code');
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    const img = new Image();
    const svgData = new XMLSerializer().serializeToString(svg);
    const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
    const url = URL.createObjectURL(svgBlob);

    img.onload = () => {
      canvas.width = svg.width.baseVal.value;
      canvas.height = svg.height.baseVal.value + 60;

      ctx.fillStyle = qrOptions.backgroundColor;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      ctx.drawImage(img, 0, 0);

      ctx.fillStyle = qrOptions.textColor;
      ctx.font = `bold ${qrOptions.textSize}px Arial`;
      ctx.textAlign = 'center';
      ctx.fillText(
        qrOptions.displayText,
        canvas.width / 2,
        canvas.height - 20
      );

      const pngUrl = canvas.toDataURL('image/png', 1.0);
      const downloadLink = document.createElement('a');
      downloadLink.href = pngUrl;
      downloadLink.download = 'qr-code.png';
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(url);
    };

    img.src = url;
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            QR Code Generator
          </h1>
          <p className="text-gray-600">
            Create custom QR codes for your business
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - QR Preview */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <div className="flex flex-col items-center justify-center space-y-6">
              <div className="relative bg-gray-50 p-8 rounded-xl w-full max-w-md">
                <QRCode
                  id="qr-code"
                  value={qrOptions.qrLink}
                  size={256}
                  fgColor={qrOptions.foregroundColor}
                  bgColor={qrOptions.backgroundColor}
                  level="H"
                  style={{
                    padding: `${qrOptions.padding}px`,
                    width: '100%',
                    height: 'auto',
                    maxWidth: '256px',
                    margin: '0 auto',
                  }}
                />
                <div
                  className="text-center mt-4 font-bold text-green-800"
                  style={{
                    fontSize: `${qrOptions.textSize}px`,
                    color: qrOptions.textColor,
                  }}
                >
                  {qrOptions.displayText}
                </div>
              </div>
              <button
                onClick={downloadQR}
                className="w-full max-w-md bg-gradient-to-r from-green-400 to-green-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-green-500 hover:to-green-700 transition duration-300 shadow-md"
              >
                Download QR Code
              </button>
            </div>
          </div>

          {/* Right Column - Controls */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold text-gray-800 mb-6">
                Customize Your QR Code
              </h2>

              {/* QR Link Input - Disabled */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  QR Code Content
                </label>
                <input
                  type="text"
                  value={qrOptions.qrLink}
                  disabled
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100 cursor-not-allowed"
                />
              </div>

              {/* Display Text Input */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Display Text
                </label>
                <input
                  type="text"
                  value={qrOptions.displayText}
                  onChange={(e) => handleOptionChange('displayText', e.target.value)}
                  placeholder="Enter text to display below QR code"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>

              {/* Colors Section */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    QR Color
                  </label>
                  <input
                    type="color"
                    value={qrOptions.foregroundColor}
                    onChange={(e) => handleOptionChange('foregroundColor', e.target.value)}
                    className="w-full h-10 rounded-lg cursor-pointer"
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Background
                  </label>
                  <input
                    type="color"
                    value={qrOptions.backgroundColor}
                    onChange={(e) => handleOptionChange('backgroundColor', e.target.value)}
                    className="w-full h-10 rounded-lg cursor-pointer"
                  />
                </div>
              </div>

              {/* Text Customization */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-800">
                  Text Customization
                </h3>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Text Color
                  </label>
                  <input
                    type="color"
                    value={qrOptions.textColor}
                    onChange={(e) => handleOptionChange('textColor', e.target.value)}
                    className="w-full h-10 rounded-lg cursor-pointer"
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Text Size: {qrOptions.textSize}px
                  </label>
                  <input
                    type="range"
                    min="8"
                    max="48"
                    value={qrOptions.textSize}
                    onChange={(e) => handleOptionChange('textSize', parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                </div>
              </div>

              {/* QR Code Settings */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-800">
                  QR Code Settings
                </h3>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Padding: {qrOptions.padding}px
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="20"
                    value={qrOptions.padding}
                    onChange={(e) => handleOptionChange('padding', parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Corner Radius: {qrOptions.cornerRadius}%
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={qrOptions.cornerRadius}
                    onChange={(e) => handleOptionChange('cornerRadius', parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                </div>
              </div>

              {/* Save/Reset Buttons */}
              <div className="mt-8 space-y-4">
                <button
                  onClick={handleSave}
                  className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg font-semibold transition duration-300 shadow-md flex items-center justify-center gap-2"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                    />
                  </svg>
                  Save QR Code Settings
                </button>

                {savedOptions && (
                  <button
                    onClick={handleReset}
                    className="w-full border-2 border-gray-300 hover:bg-gray-50 text-gray-700 py-3 px-6 rounded-lg font-semibold transition duration-300 flex items-center justify-center gap-2"
                  >
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                    Reset Changes
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ORCode;
