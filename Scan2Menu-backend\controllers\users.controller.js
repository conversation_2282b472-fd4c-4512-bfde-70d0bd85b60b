const express = require('express');
const User = require('../models/user.model');
const Restaurant = require("../models/restaurant.model");
const Staff = require('../models/staff.model');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');
const BlacklistToken = require('../models/blacklistToken.model');
const userValidationSchema = require('../validators/userValidator');  // Joi schema for validation
const sendEmail = require('../utils/email');
const Joi = require('joi');
const crypto = require('crypto');
const { OAuth2Client } = require('google-auth-library');
const { decode } = require('punycode');
const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);
const { resetPasswordTemplate } = require('../service/email/templates/resetPassword.template');
// Joi schema for validating password
const passwordSchema = Joi.object({
  password: Joi.string()
    .min(6)
    .max(30)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{6,}$/)
    .required()
    .messages({
      'string.min': '"password" should have a minimum length of 6 characters',
      'string.max': '"password" should have a maximum length of 30 characters',
      'string.pattern.base': '"password" must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      'any.required': '"password" is a required field'
    })
});


// Controller to get all users
exports.getAllUsers = async (req, res, next) => {
  try {
    const users = await User.find().select('-password');;
    res.status(200).json(users);
  } catch (err) {
    next(err);
  }
};

exports.getSingleUser = async (req, res, next) => {
  try {
    const param = req.params.param;
    let user;

    // 🔹 Try to find user by ID
    if (mongoose.Types.ObjectId.isValid(param)) {
      user = await User.findById(param).select('-password');
    } else {
      // 🔹 Search by username or email (case-insensitive)
      user = await User.findOne({
        $or: [{ username: new RegExp(`^${param}$`, 'i') }, { email: new RegExp(`^${param}$`, 'i') }]
      }).select('-password');
    }

    if (!user) {
      return res.status(404).json({ message: 'User not found', success: false });
    }

    // 🔹 If user is `chef` or `waiter`, fetch associated staff details
    let staffDetails = null;
    if (user.role === 'chef' || user.role === 'waiter') {
      staffDetails = await Staff.findOne({ userId: user._id });
    }

    res.status(200).json({
      user,
      staff: staffDetails, // Include staff details if applicable
      success: true
    });
  } catch (err) {
    next(err);
  }
};

// Controller to create a new user
exports.createUser = async (req, res, next) => {
  try {
    // Validate using imported userValidationSchema
    const { error } = userValidationSchema.validate(req.body);

    if (error) {
      const errors = error.details.map(detail => detail.message);
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors,
        success: false
      });
    }

    const { name, username, email, phone, password, role } = req.body;

    // Check if the username, email, or phone already exists
    const existingUser = await User.findOne({ $or: [{ username }, { email }, { phone }] });
    if (existingUser) {
      const conflictFields = [];
      if (existingUser.username.toLowerCase() === username.toLowerCase()) {
        conflictFields.push('Username');
      }
      if (existingUser.email.toLowerCase() === email.toLowerCase()) {
        conflictFields.push('Email');
      }
      if (existingUser.phone === phone) {
        conflictFields.push('Phone number');
      }

      return res.status(400).json({
        message: `${conflictFields.join(', ')} already exists`,
        success: false
      });
    }
    if (!['admin', 'chef', 'waiter', 'user'].includes(role)) {
      return res.status(400).json({
        message: 'Invalid role specified',
        success: false
      });
    }
    // Hash the password before saving
    const hashedPassword = await bcrypt.hash(password, 10);

    const newUser = new User({
      name,
      username,
      email: email.toLowerCase(), // Store email in lowercase
      phone,
      password: hashedPassword,
      role
    });

    await newUser.save();

    let userResponse = newUser.toObject();
    delete userResponse.password;

    if (["chef", "waiter"].includes(role)) {

      const { restaurant_detail, working_hours, shift } = req.body;
      const userId = userResponse._id;

      if (!userId || !restaurant_detail?.id || !restaurant_detail?.slug) {
        return res.status(400).json({ message: 'User ID and Restaurant details are required', success: false });
      }

      const userExists = await User.findById(userId);
      if (!userExists) {
        return res.status(404).json({ message: 'User not found', success: false });
      }

      const newStaff = new Staff({
        working_hours: working_hours || 0,
        userId,
        shift,
        restaurant_detail,
        status: 'inactive', // Default status

      });
      const populatedStaff = await newStaff.save();
      userResponse = { ...userResponse, staffDetails: populatedStaff.toObject() };
    }

    res.status(201).json({ message: 'User created successfully', user: userResponse, success: true });
  } catch (err) {
    next(err);
  }
};

// Controller to login a user
exports.loginUser = async (req, res, next) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ message: 'All fields are required', success: false });
    }

    // 🔹 Case-insensitive search for username
    const user = await User.findOne({ username: new RegExp(`^${username}$`, 'i') });

    if (!user) {
      return res.status(404).json({ message: 'User not found', success: false });
    }

    // 🔹 Ensure user is active
    if (user.status === 'inactive') {
      return res.status(403).json({ message: 'Account is inactive. Please contact support.', success: false });
    }

    // 🔹 Validate password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(400).json({ message: 'Invalid credentials', success: false });
    }

    // 🔹 Generate JWT token
    const token = jwt.sign(
      { id: user._id, username: user.username, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    // 🔹 Remove sensitive data
    const userResponse = user.toObject();
    delete userResponse.password;

    let response = {
      message: "Login successful",
      token,
      user: userResponse,
      success: true
    };

    // 🔹 Attach restaurant details if the user is an admin/owner
    if (user.role === 'admin') {
      const restaurant = await Restaurant.findOne({ userId: user._id });
      if (restaurant) {
        response.restaurant = restaurant;
      }
    }

    // 🔹 Attach staff details if the user is a `chef` or `waiter`
    if (user.role === 'chef' || user.role === 'waiter') {
      const staff = await Staff.findOne({ userId: user._id });
      if (staff) {
        response.user.restaurant_detail = staff.restaurant_detail;
        response.user.staffID = staff._id;
        response.user.status = staff.status;
      }
    }

    res.status(200).json(response);
  } catch (err) {
    next(err);
  }
};


//controler to logout user
exports.logoutUser = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      return res.status(401).json({ message: 'Authorization header missing', success: false });
    }

    const token = authHeader.split(' ')[1];

    // 🔹 Check if token is already blacklisted
    const existingToken = await BlacklistToken.findOne({ token });
    if (existingToken) {
      return res.status(400).json({ message: 'Token already blacklisted', success: false });
    }

    // 🔹 Add token to blacklist
    await BlacklistToken.create({ token });

    res.status(200).json({
      message: 'User logged out successfully',
      success: true
    });
  } catch (error) {
    next(error);
  }
};

// Controller to delete a user by ID, username, or email
exports.deleteUser = async (req, res, next) => {
  try {
    const { param } = req.params;
    // 🔹 Validate if param is a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(param)) {
      return res.status(400).json({ success: false, message: "Invalid User ID" });
    }

    const loggedUserRole = req.user.role;
    const loggedUserId = req.user.id;

    // 🔹 Find the user to delete
    const userToDelete = await User.findById(param);

    if (!userToDelete) {
      return res.status(404).json({ success: false, message: "User not found" });
    }

    // 🔹 Prevent admins from deleting themselves
    if (loggedUserId === userToDelete._id.toString()) {
      return res.status(403).json({ success: false, message: "You cannot delete your own account" });
    }

    // 🔹 Only allow admin to delete `chef` or `waiter`
    if (userToDelete.role === "chef" || userToDelete.role === "waiter") {
      if (loggedUserRole !== "admin") {
        return res.status(403).json({ success: false, message: "Only admin can delete chefs or waiters" });
      }

      // 🔹 Delete the corresponding staff record
      await Staff.deleteOne({ userId: userToDelete._id });
    }

    // 🔹 Delete the user
    await User.findByIdAndDelete(param);

    res.status(200).json({ success: true, message: "User deleted successfully" });
  } catch (err) {
    next(err);
  }
};

exports.updateUser = async (req, res, next) => {
  try {
    const { name, username, email, phone, password, status, address, shift, restaurant_detail } = req.body;
    const userId = req.params.param;
    const updaterRole = req.user.role;
    const updaterId = req.user._id;

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({ message: 'Invalid user ID', success: false });
    }

    // Find the user to be updated
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found', success: false });
    }

    // 🔹 Restrict Admins from updating other Admins
    if (updaterRole === 'admin' && user.role === 'admin' && updaterId.toString() !== userId) {
      return res.status(403).json({ message: 'Admins can only update their own profile', success: false });
    }

    // 🔹 Restrict Waiters & Chefs from updating other users
    if ((updaterRole === 'waiter' || updaterRole === 'chef') && updaterId.toString() !== userId) {
      return res.status(403).json({ message: 'You can only update your own details', success: false });
    }

    // 🔹 Prevent duplicate username, email, and phone
    // if (username) {
    //   const existingUsername = await User.findOne({ _id: { $ne: userId }, username: username.toLowerCase() });
    //   if (existingUsername) {
    //     return res.status(400).json({ message: 'Username already exists', success: false });
    //   }
    // }
    if (username) {
      return res.status(400).json({ message: 'Username cannot be changed', success: false });
    }    
    if (email) {
      const existingEmail = await User.findOne({ _id: { $ne: userId }, email: email.toLowerCase() });
      if (existingEmail) {
        return res.status(400).json({ message: 'Email already exists', success: false });
      }
    }
    if (phone) {
      const existingPhone = await User.findOne({ _id: { $ne: userId }, phone });
      if (existingPhone) {
        return res.status(400).json({ message: 'Phone number already exists', success: false });
      }
    }

    // 🔹 Update User Model
    user.name = name || user.name;
    user.username = username || user.username;
    user.email = email ? email.toLowerCase() : user.email;
    user.phone = phone || user.phone;
    user.status = status || user.status;

    // 🔹 Admin can update roles (except other admins)
    if (updaterRole === 'admin' && user.role !== 'admin') {
      user.role = req.body.role || user.role;
    }

    // 🔹 Handle password update
    if (password) {
      const { error } = passwordSchema.validate({ password });
      if (error) {
        return res.status(400).json({ message: error.details[0].message, success: false });
      }
      user.password = await bcrypt.hash(password, 10);
    }

    await user.save();

    // 🔹 If the user is a staff member (waiter/chef) or ADMIN is updating staff details
    if (user.role === 'waiter' || user.role === 'chef') {
      let staff = await Staff.findOne({ userId });

      if (!staff) {
        return res.status(404).json({ message: 'Staff record not found', success: false });
      }

      // ✅ Allow Admins to update ALL staff fields
      if (updaterRole === 'admin') {
        if (status !== undefined) staff.status = status;
        if (address) staff.address = address;
        if (shift) staff.shift = shift;

        if (restaurant_detail) {
          staff.restaurant_detail.id = restaurant_detail.id || staff.restaurant_detail.id;
          staff.restaurant_detail.slug = restaurant_detail.slug || staff.restaurant_detail.slug;
        }
      }

      // ✅ Allow staff to update their own details (but not restaurant details)
      if (updaterId.toString() === userId) {
        if (address) staff.address = address;
        if (shift) staff.shift = shift;
      }

      await staff.save();

    }

    return res.status(200).json({
      message: 'User updated successfully',
      user: { ...user.toObject(), password: undefined }, // Remove password from response
      success: true
    });

  } catch (err) {
    next(err);
  }
};

//forgot password route
exports.forgotPassword = async (req, res, next) => {
  try {
    const { email } = req.body;
    if (!email) return res.status(400).json({ message: 'Email is required', success: false });

    const user = await User.findOne({ email });
    if (!user) return res.status(404).json({ message: 'User not found', success: false });

    // Generate JWT reset token (valid for 15 minutes)
    const resetToken = jwt.sign(
      { id: user._id, email: user.email },
      process.env.JWT_RESET_SECRET,
      { expiresIn: '10m' }
    );

    const resetLink = `https://scan-2-menu.netlify.app/reset-password?token=${resetToken}&email=${email}`;
    // const resetLink = `http://localhost:5173/reset-password?token=${resetToken}&email=${email}`;
 
     
    const emailTemplate = resetPasswordTemplate(email, resetLink);
    const plainText = `Scan2Menu Password Reset\n\nYou requested a password reset. Click the link below to reset your password:\n\n${resetLink}\n\nIf you did not request this, ignore this email.`;

    await sendEmail({
      to: email,
      subject: 'Reset Your Password - Scan2Menu',
      text: plainText,
      html: emailTemplate,
    });

    res.status(200).json({ message: 'Password reset link sent to email', success: true });
  } catch (error) {
    next(error);
  }
};

// Reset Password Route
exports.resetPassword = async (req, res, next) => {
  try {
    const { token, newPassword } = req.body;
    console.log("token", token)
    if (!token || !newPassword) return res.status(400).json({ message: 'Invalid request', success: false });

    // Check if the token was already used (revoked)
    const isBlacklisted = await BlacklistToken.findOne({ token });
    if (isBlacklisted) return res.status(400).json({ message: 'Token already used', success: false });

    // Verify JWT token
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_RESET_SECRET);
    } catch (err) {
      return res.status(400).json({ message: 'Invalid or expired token', success: false });
    }
    console.log("decode", decode);

    // Find user and update password
    const user = await User.findById(decoded.id);
    if (!user) return res.status(404).json({ message: 'User not found', success: false });

    user.password = await bcrypt.hash(newPassword, 10);
    await user.save();

    // Blacklist the used token
    await BlacklistToken.create({ token });

    res.status(200).json({ message: 'Password reset successful', success: true });
  } catch (error) {
    next(error);
  }
};


// Controller to get all restaurant staff
exports.getAllRestaurantStaff = async (req, res, next) => {

  try {
    const restaurantId = req.params.id;

    const staff = await Staff.find({ 'restaurant_detail.id': restaurantId })
      .populate({ path: 'userId', select: 'name username phone email role' });

    if (staff.length === 0) {
      return res.status(404).json({ message: 'No staff found for this restaurant', success: false });
    }

    // Transform data to match the required format
    const formattedStaff = staff.map(staffMember => ({
      userID: staffMember.userId?._id,
      name: staffMember.userId?.name || null,
      username: staffMember.userId?.username || null,
      phone: staffMember.userId?.phone || null,
      email: staffMember.userId?.email || null,
      role: staffMember.userId?.role || null,
      createdAt: staffMember.createdAt,
      updatedAt: staffMember.updatedAt,
      restaurant_detail: {
        id: staffMember.restaurant_detail.id,
        slug: staffMember.restaurant_detail.slug
      },
      status: staffMember.status,
      shift: staffMember.shift,
      working_hours: staffMember.working_hours
    }));

    return res.status(200).json({ data: formattedStaff, success: true });
  } catch (error) {
    next(error);

  }
}

// Controller for Google OAuth
exports.googleAuth = async (req, res, next) => {
  try {
    const { token } = req.body;

    // Verify the Google token
    const ticket = await client.verifyIdToken({
      idToken: token,
      audience: process.env.GOOGLE_CLIENT_ID
    });

    const { name, email, picture, sub: googleId } = ticket.getPayload();

    // Check if user exists with googleId
    let user = await User.findOne({ googleId });

    if (!user) {
      // Check if email exists
      user = await User.findOne({ email });

      if (user) {
        // Link Google account to existing user
        user.googleId = googleId;
        user.picture = picture;
        await user.save();
      } else {
        // Create new user with a unique username
        const baseUsername = email.split('@')[0];
        let username = baseUsername;
        let counter = 1;

        // Keep trying until we find a unique username
        while (await User.findOne({ username })) {
          username = `${baseUsername}_${counter}`;
          counter++;
        }

        // Create new user without phone field for Google auth
        const newUser = {
          name,
          email,
          username,
          googleId,
          picture,
          role: 'admin',
          status: 'active'
          // Don't set phone field at all for Google auth
        };

        user = await User.create(newUser);
      }
    }

    // Create JWT token
    const jwtToken = jwt.sign(
      { id: user._id, username: user.username },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Remove password from user object
    const userResponse = user.toObject();
    delete userResponse.password;

    // Check if user has any associated restaurants
    const restaurant = await Restaurant.findOne({ userId: user._id });

    const response = {
      message: "Google Login Successful",
      token: jwtToken,
      user: userResponse,
      success: true
    };

    if (restaurant) {
      response.restaurant = restaurant;
    }

    res.status(200).json(response);
  } catch (err) {
    console.error('Google Auth Error:', err);
    next(err);
  }
};

// Controller for staff check-in/out
exports.updateStaffCheckInOut = async (req, res, next) => {
  try {
    const userId = req.params.id;
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({ message: 'Invalid user ID', success: false });
    }
    const { start_time, end_time } = req.body;
    // console.log(start_time, end_time)
    const staff = await Staff.findOne({ userId: userId });
    if (!staff) {
      return res.status(404).json({ message: 'Staff not found', success: false });
    }
    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(today.getDate() - 1);

    // Format date for comparison
    const todayStr = today.toDateString();
    const yesterdayStr = yesterday.toDateString();

    // Find yesterday's attendance
    let yesterdayAttendance = staff.attendance.find(attendance =>
      new Date(attendance.date).toDateString() === yesterdayStr
    );

    // Find today's attendance
    let todayAttendance = staff.attendance.find(attendance =>
      new Date(attendance.date).toDateString() === todayStr
    );

    // If yesterday’s attendance exists and has no end_time, mark as still checked in
    if (yesterdayAttendance && yesterdayAttendance.start_time && !yesterdayAttendance.end_time) {
      staff.status = 'inactive';
      yesterdayAttendance.end_time = end_time;
      yesterdayAttendance.isClockedIn = false;

      // Calculate working hours
      const start = new Date(yesterdayAttendance.start_time);
      const end = new Date(end_time);
      const diff = (end - start) / (1000 * 60 * 60);

      // Set presence based on working hours
      if (diff >= staff.working_hours) {
        yesterdayAttendance.presence = 'full day';
      } else if (diff >= staff.working_hours / 2) {
        yesterdayAttendance.presence = 'half day';
      } else {
        yesterdayAttendance.presence = 'leave';
      }

    } else if (!todayAttendance) {
      // If no record for today, create a new attendance entry
      staff.attendance.push({
        date: today,
        presence: 'present',
        start_time,
        isClockedIn: true
      });
      staff.status = 'active';

    } else if (todayAttendance.isClockedIn && !todayAttendance.end_time) {
      // If today’s attendance exists but has no end_time, update it
      staff.status = 'inactive';
      todayAttendance.end_time = end_time;
      todayAttendance.isClockedIn = false;

      // Calculate working hours
      const start = new Date(todayAttendance.start_time);
      const end = new Date(end_time);
      const diff = (end - start) / (1000 * 60 * 60);

      // Set presence based on working hours
      if (diff >= staff.working_hours) {
        todayAttendance.presence = 'full day';
      } else if (diff >= staff.working_hours / 2) {
        todayAttendance.presence = 'half day';
      } else {
        todayAttendance.presence = 'leave';
      }
    } else {
      return res.status(400).json({ message: "Invalid attendance update", success: false });
    }

    await staff.save();

    return res.status(200).json({ message: 'Attendance updated successfully', success: true, staff });
  } catch (error) {
    next(error);
  }
}

