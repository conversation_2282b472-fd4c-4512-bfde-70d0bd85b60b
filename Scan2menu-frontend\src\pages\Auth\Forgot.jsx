import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { useNavigate, useParams } from 'react-router-dom';
import { Loader2 } from 'lucide-react';
import InputField from '../../components/InputFieldValidation/InputField';
import { Button } from '../../components/ui/button';

const NewPassword = () => {
  const navigate = useNavigate();
  const { token } = useParams();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '',
  });

  const validateForm = () => {
    const newErrors = {};

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Confirm Password is required';
    } else if (formData.confirmPassword !== formData.password) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      setLoading(true);
      try {
        // Simulate API call
        setTimeout(() => {
          toast.success('Password has been reset successfully');
          navigate('/login');
        }, 2000);
      } catch (error) {
        toast.error('Something went wrong, please try again');
      } finally {
        setLoading(false);
      }
    }
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    if (errors[e.target.name]) {
      setErrors({
        ...errors,
        [e.target.name]: '',
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FFF5E6] to-[#FFE5CC] py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="bg-white rounded-3xl shadow-2xl overflow-hidden">
          <div className="px-6 py-8 text-center">
            <h1 className="text-3xl font-bold text-[#EFA052]">Scan2Menu</h1>
            <h2 className="text-2xl font-semibold text-gray-800">Set New Password</h2>
            <p className="mt-2 text-gray-600">Enter your new password below</p>
          </div>

          <form onSubmit={handleSubmit} className="px-6 pb-8">
            <div className="space-y-4">
              <InputField
                label="New Password"
                type="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                error={errors.password}
                placeholder="Enter new password"
                className="rounded-xl border-gray-300 focus:border-[#EFA052] focus:ring focus:ring-[#EFA052] focus:ring-opacity-50"
              />

              <InputField
                label="Confirm Password"
                type="password"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                error={errors.confirmPassword}
                placeholder="Confirm new password"
                className="rounded-xl border-gray-300 focus:border-[#EFA052] focus:ring focus:ring-[#EFA052] focus:ring-opacity-50"
              />

              <Button
                type="submit"
                className="w-full py-3 font-medium text-white bg-[#EFA052] hover:bg-[#D88A3B] rounded-xl transition-colors"
                disabled={loading}
              >
                {loading ? (
                  <div className="flex items-center justify-center gap-2">
                    <Loader2 className="animate-spin" size={20} />
                    <span>Saving...</span>
                  </div>
                ) : (
                  'Save Password'
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default NewPassword;
