const Order = require('../models/order.model');
const Table = require('../models/table.model');
const Staff = require('../models/staff.model')

// Get all orders by restaurant ID with pagination and optional date filter
exports.getOrdersByRestaurantId = async (req, res, next) => {
  try {
    const { restaurantId } = req.params;
    const { page = 1, limit = 10, date } = req.query;
    let filter = { restaurantId };

    // Add date filter if provided
    if (date) {
      const [day, month, year] = date.split('-');
      const startDate = new Date(`${year}-${month}-${day}T00:00:00.000Z`);
      const endDate = new Date(`${year}-${month}-${day}T23:59:59.999Z`);

      filter.createdAt = {
        $gte: startDate,
        $lte: endDate
      };
    }

    const pageNum = Math.max(1, parseInt(page));
    const limitNum = Math.max(1, parseInt(limit));
    const skip = (pageNum - 1) * limitNum;

    const [orders, totalOrders] = await Promise.all([
      Order.find(filter)
        .populate({
          path: 'items.itemId',
          select: 'title price'
        })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limitNum)
        .exec(),
      Order.countDocuments(filter)
    ]);

    const totalPages = Math.ceil(totalOrders / limitNum) || 1;

    res.status(200).json({
      success: true,
      orders,
      pagination: {
        total: totalOrders,
        page: pageNum,
        pages: totalPages,
        limit: limitNum,
        currentPageSize: orders.length
      }
    });
  } catch (error) {
    console.error('Pagination Error:', error);
    next(error);
  }
};

// Get order by order ID
exports.getOrderById = async (req, res, next) => {
  try {
    const { orderId } = req.params;
    const order = await Order.findById(orderId).populate({
      path: 'items.itemId',
      select: 'title' // Include additional fields you want
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found',
      });
    }

    res.status(200).json({
      success: true,
      order,
    });
  } catch (error) {
    next(error);
  }
};

// Create a new order
exports.createOrder = async (req, res, next) => {
  try {

    const { name, tableNumber, phoneNumber, subtotal, gst, total, items, restaurantId } = req.body;
    let rolee = 'user';
    if (!name || !tableNumber || !phoneNumber || !items || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Name, table number, phone number, and items are required',
      });
    }

    if (req.user) {
      rolee = req.user._id;
    }
    const newOrder = new Order({
      restaurantId,
      name,
      tableNumber,
      phoneNumber,
      subtotal,
      gst,
      total,
      items,
      orderby: rolee
    });
    console.log(newOrder)
    await newOrder.save();

    // After saving the order, update the table status
    await Table.findOneAndUpdate(
      { restaurantId, tableNumber },
      {
        status: 'occupied',
        currentOrder: newOrder._id
      }
    );
    //if order by the userID
    if (rolee != "user") {
      await Staff.findOneAndUpdate(
        { userId: rolee },
        {
          $push: {
            order_history: {
              orderId: newOrder._id,
              date: newOrder.createdAt
            }
          }
        },
        { new: true }
      );
    }

    // Emit socket event if socket instance exists
    // if (req.io) {
    //   const updatedTable = await Table.findOne({ restaurantId, tableNumber })
    //     .populate({
    //       path: 'currentOrder',
    //       select: 'name phoneNumber items status total'
    //     });
    //   req.io.emit('tableStatusChanged', updatedTable);
    // }

    const order = await Order.findById(newOrder._id).populate({ path: 'items.itemId', select: 'title price' })

    res.status(201).json({
      success: true,
      message: 'Order created successfully',
      order,
    });

  } catch (error) {
    next(error);
  }
};

// Update an order
exports.updateOrderStatus = async (req, res, next) => {
  try {
    const { orderId } = req.params;
    const { status } = req.body;

    // Only allow updating the status field
    if (!status || !['pending', 'preparing', 'served', 'completed', 'cancelled'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Only valid status updates are allowed',
      });
    }

    const updatedOrder = await Order.findByIdAndUpdate(
      orderId,
      { status },
      { new: true }
    ).populate('items.itemId', '_id title');

    if (!updatedOrder) {
      return res.status(404).json({
        success: false,
        message: 'Order not found',
      });
    }
    if (status === 'completed' || status === 'cancelled') {
      await Table.findOneAndUpdate(
        { currentOrder: orderId },
        { status: 'available', currentOrder: null }
      );

    }
    res.status(200).json({
      success: true,
      message: 'Order status updated successfully',
      order: updatedOrder,
    });
  } catch (error) {
    next(error);
  }
};

// Delete an order
exports.deleteOrder = async (req, res, next) => {
  try {
    const { orderId } = req.params;
    const deletedOrder = await Order.findByIdAndDelete(orderId);

    if (!deletedOrder) {
      return res.status(404).json({
        success: false,
        message: 'Order not found',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Order deleted successfully',
    });
  } catch (error) {
    next(error);
  }
};

// Get orders by filter (date, time, etc.)
exports.getOrdersByFilter = async (req, res, next) => {
  try {
    const { date, page = 1, limit = 10 } = req.query;
    const { restaurantId } = req.params;

    let filter = { restaurantId };

    if (date) {
      // Convert date from DD-MM-YYYY to YYYY-MM-DD format for JavaScript Date
      const [day, month, year] = date.split('-');
      const startDate = new Date(`${year}-${month}-${day}T00:00:00.000Z`);
      const endDate = new Date(`${year}-${month}-${day}T23:59:59.999Z`);

      filter.createdAt = {
        $gte: startDate,
        $lte: endDate
      };
    }

    const pageNum = Math.max(1, parseInt(page));
    const limitNum = Math.max(1, parseInt(limit));
    const skip = (pageNum - 1) * limitNum;

    const [orders, totalOrders] = await Promise.all([
      Order.find(filter)
        .populate('items.itemId', '_id title')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limitNum)
        .exec(),
      Order.countDocuments(filter)
    ]);

    const totalPages = Math.ceil(totalOrders / limitNum) || 1;

    res.status(200).json({
      success: true,
      orders,
      pagination: {
        total: totalOrders,
        page: pageNum,
        pages: totalPages,
        limit: limitNum,
        currentPageSize: orders.length
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get all orders of waiter
exports.getOrdersByWaiter = async (req, res, next) => {
  try {
    const { id } = req.params;
    const orders = await Order.find({ orderby: id }).populate({ path: 'items.itemId', select: 'title' });
    res.status(200).json({
      success: true,
      orders,
    });
  }
  catch (error) {
    next(error);
  }
};
