import React, { useState } from 'react';
import { format } from 'date-fns';

const Notifications = () => {
  // Sample notifications data - replace with your actual data
  const [notifications] = useState([
    {
      id: 1,
      type: 'order',
      message: 'New order received for Table 5',
      time: '2024-03-15T09:30:00',
      isRead: false
    },
    {
      id: 2,
      type: 'stock',
      message: 'Menu item "Paneer Tikka" is low on stock',
      time: '2024-03-15T09:15:00',
      isRead: false
    },
    {
      id: 3,
      type: 'feedback',
      message: 'Customer feedback received',
      time: '2024-03-15T09:00:00',
      isRead: true
    },
    {
      id: 4,
      type: 'system',
      message: 'System maintenance scheduled for tonight',
      time: '2024-03-15T08:45:00',
      isRead: true
    }
  ]);

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'order':
        return '🛍️';
      case 'stock':
        return '📦';
      case 'feedback':
        return '💬';
      case 'system':
        return '⚙️';
      default:
        return '📌';
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Notifications</h1>
        <p className="text-gray-600">Stay updated with your restaurant's activities</p>
      </div>

      <div className="space-y-4">
        {notifications.map((notification) => (
          <div 
            key={notification.id} 
            className={`p-4 rounded-xl shadow-sm border ${
              notification.isRead ? 'bg-white' : 'bg-blue-50 border-blue-100'
            }`}
          >
            <div className="flex items-start gap-4">
              <span className="text-2xl">{getNotificationIcon(notification.type)}</span>
              <div className="flex-1">
                <p className={`text-gray-800 ${!notification.isRead && 'font-semibold'}`}>
                  {notification.message}
                </p>
                <p className="text-sm text-gray-500 mt-1">
                  {format(new Date(notification.time), 'MMM d, yyyy h:mm a')}
                </p>
              </div>
              {!notification.isRead && (
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Notifications; 