exports.resetPasswordTemplate = (email, resetLink) => {
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Password Reset - Scan2Menu</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f4f4f4;
      margin: 0;
      padding: 0;
      color: #333;
    }
    .container {
      max-width: 600px;
      margin: 20px auto;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    .header {
      background-color: #EFA052;
      padding: 20px;
      text-align: center;
    }
    .header h1 {
      margin: 0;
      font-size: 24px;
      color: #ffffff;
      font-family: 'Galmoru', Arial, sans-serif;
    }
    .content {
      padding: 30px;
      text-align: left;
    }
    .content p {
      font-size: 16px;
      line-height: 1.6;
    }
    .button {
      display: block;
      width: 200px;
      margin: 20px auto;
      padding: 12px 0;
      background-color: #EFA052;
      color: #ffffff;
      text-align: center;
      text-decoration: none;
      border-radius: 5px;
      font-size: 16px;
    }
    .footerr {
      background-color: #333333;
      color: #ffffff;
      text-align: center;
      padding: 15px;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Scan2Menu</h1>
    </div>
    <div class="content">
      <p>Dear User,</p>
      <p>We received a request to reset the password for your account associated with <strong>${email}</strong>.</p>
      <p>Please click the button below to reset your password. This link will expire in 10 minutes.</p>
      <a href="${resetLink}" class="button">Reset Password</a>
      <p>If the button above doesn't work, copy and paste the following link into your browser:</p>
      <p><a href="${resetLink}">${resetLink}</a></p>
      <p>If you did not request a password reset, please ignore this email or contact our support team if you have questions.</p>
    </div>
    <div class="footerr">
      &copy; 2025 Scan2Menu. All rights reserved.
    </div>
  </div>
</body>
</html>
`
};