const mongoose = require('mongoose');

// Error handling middleware
const errorhandler = (err, req, res, next) => {
    console.error(err.stack);
  
    if (err instanceof mongoose.Error.ValidationError) {
      return res.status(400).json({ status: 'fail', message: err.message, success: false });
    }
  
    if (err instanceof mongoose.Error.CastError) {
      return res.status(400).json({ 
        status: 'fail', 
        message: 'Invalid ID format', 
        success: false 
      });
    }
  
    if (err.code === 11000) {
      return res.status(400).json({ status: 'fail', message: 'Duplicate field value', success: false });
    }
  
    res.status(err.statusCode || 500).json({
      status: 'error',
      message: err.message || 'Internal Server Error',
      success: false
    });
  };
  
module.exports = errorhandler;


//throw mongoose error like this  

// throw new mongoose.Error.ValidationError('User data is invalid');


// throw simple error like this 

// const err = new Error('Something went wrong');
// err.statusCode = 400;
// next(err);