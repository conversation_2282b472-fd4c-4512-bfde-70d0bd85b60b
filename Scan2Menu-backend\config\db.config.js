const mongoose = require('mongoose');

const connectDB = async () => {
  try {
    // Connecting to MongoDB using the connection string from .env file
    // const conn = await mongoose.connect(process.env.MONGODB_URI);
    const conn = await mongoose.connect(process.env.MONGODB_URI_ONLINE);

    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error(`Error: ${error.message}`);
    process.exit(1); // Exit process with failure
  }
};

// Export the connectDB function
module.exports = connectDB;
