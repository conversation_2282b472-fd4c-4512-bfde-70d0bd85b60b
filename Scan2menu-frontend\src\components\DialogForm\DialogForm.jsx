// import React, { useEffect, useState } from 'react';
// import Dialog from '@mui/material/Dialog';
// import DialogActions from '@mui/material/DialogActions';
// import DialogContent from '@mui/material/DialogContent';
// import DialogContentText from '@mui/material/DialogContentText';
// import DialogTitle from '@mui/material/DialogTitle';
// import { Button } from '../ui/button';
// import InputField from '../InputFieldValidation/InputField';

// const FormDialog = ({ open, onClose, DialogType, handleDialogFormDataSubmit }) => {
//     const [error, setError] = useState({});
//     const [formValue, setFormValue] = useState("");

//     const validate = (value) => {
//         const errors = {};
//         if (!value) {
//             errors[DialogType] = `${DialogType} is required`;
//         } else if (value.length < 3) {
//             errors[DialogType] = `${DialogType} must be at least 3 characters long`;
//         }
//         return errors;
//     };

//     const handleChange = (event) => {
//         const { value, name } = event.target;
//         setFormValue(value);
//         if (error[name]) {
//             setError({
//                 ...error,
//                 [name]: ''
//             });
//         }
//     };

//     const handleSubmit = (event) => {
//         event.preventDefault();
//         const validationErrors = validate(formValue);
//         if (Object.keys(validationErrors).length > 0) {
//             setError(validationErrors);
//             return;
//         }

//         handleDialogFormDataSubmit(formValue);
//         onClose();
//     };

//     return (
//         <Dialog
//             open={open}
//             onClose={onClose}
//             PaperProps={{
//                 component: "form",
//                 onSubmit: handleSubmit,
//             }}
//         >
//             <DialogTitle>Add {DialogType}</DialogTitle>
//             <DialogContent>
//                 <DialogContentText>
//                     Enter the name of the new category you want to add.
//                 </DialogContentText>
//                 <InputField
//                     lable={DialogType}
//                     name={DialogType}
//                     onChange={handleChange}
//                     error={error[DialogType] || null}
//                     placeholder="Enter category name"
//                     className="w-full"

//                 />
//             </DialogContent>
//             <DialogActions>
//                 <Button className='' onClick={onClose}>Cancel</Button>
//                 <Button className='bg-sky-600' type="submit">Add</Button>

//             </DialogActions>
//         </Dialog>
//     );
// };

// export default FormDialog;

import React, { useEffect, useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import { Button } from '../ui/button';
import InputField from '../InputFieldValidation/InputField';

const FormDialog = ({ open, onClose, DialogType, initialFormValue = "", handleDialogFormDataSubmit }) => {
    const [error, setError] = useState({});
    const [formValue, setFormValue] = useState(initialFormValue);

    useEffect(() => {
        setFormValue(initialFormValue);
    }, [initialFormValue]);

    const validate = (value) => {
        const errors = {};
        if (!value) {
            errors[DialogType] = `${DialogType} is required`;
        } else if (value.length < 3) {
            errors[DialogType] = `${DialogType} must be at least 3 characters long`;
        }
        return errors;
    };

    const handleChange = (event) => {
        const { value, name } = event.target;
        setFormValue(value);
        if (error[name]) {
            setError({
                ...error,
                [name]: ''
            });
        }
    };

    const handleSubmit = (event) => {
        event.preventDefault();
        const validationErrors = validate(formValue);
        if (Object.keys(validationErrors).length > 0) {
            setError(validationErrors);
            return;
        }

        handleDialogFormDataSubmit(formValue);
        setFormValue("");
        onClose();
    };

    return (
        <Dialog
            open={open}
            onClose={onClose}
            PaperProps={{
                component: "form",
                onSubmit: handleSubmit,
            }}
        >
            <DialogTitle>{initialFormValue ? `Update ${DialogType}` : `Add ${DialogType}`}</DialogTitle>
            <DialogContent>
                <DialogContentText>
                    Enter the name of the {DialogType.toLowerCase()} you want to {initialFormValue ? 'update' : 'add'}.
                </DialogContentText>
                <InputField
                    label={DialogType}
                    name={DialogType}
                    onChange={handleChange}
                    value={formValue}
                    error={error[DialogType] || null}
                    placeholder={`Enter ${DialogType.toLowerCase()} name`}
                    className="w-full"
                />
            </DialogContent>
            <DialogActions>
                <Button className='' onClick={onClose}>Cancel</Button>
                <Button className='bg-sky-600' type="submit">{initialFormValue ? 'Update' : 'Add'}</Button>
            </DialogActions>
        </Dialog>
    );
};

export default FormDialog;