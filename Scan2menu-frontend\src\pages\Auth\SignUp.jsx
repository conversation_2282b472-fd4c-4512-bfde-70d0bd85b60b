import { useState, useContext, useEffect } from 'react';
import InputField from '../../components/InputFieldValidation/InputField';
import { postSignupData, googleAuth } from '../../api'
import { toast } from 'react-hot-toast';
import { Link } from 'react-router-dom';
import { Loader2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { UserDataContext } from '../../context/UserContext';
import { RestaurantDataContext } from '../../context/RestaurantContext';
import { Button } from '../../components/ui/button';
import { GoogleLogin } from '@react-oauth/google';

const SignUp = () => {
  const { user, setUser } = useContext(UserDataContext);
  const { setRestaurantData } = useContext(RestaurantDataContext);
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (localStorage.getItem('token') && user) {
      redirectBasedOnRole(user);
    }
  }, [user, navigate]);

  const redirectBasedOnRole = (userData) => {
    if (userData.role === 'admin') {
      navigate('/');
    } else if (userData.role === 'waiter' || userData.role === 'chef') {
      navigate('/staff/dashboard');
    }
  };

  const [formData, setFormData] = useState({
    name: '',
    username: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    role: 'admin'
  });
  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name) {
      newErrors.name = 'Name name is required';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Name must be at least 2 characters long';
    }

    if (!formData.username) {
      newErrors.username = 'Username is required';
    } else if (formData.username.length < 3) {
      newErrors.username = 'Username must be at least 3 characters';
    }

    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    if (!formData.phone) {
      newErrors.phone = 'Phone number is required';
    } else if (formData.phone.length < 10) {
      newErrors.phone = 'Phone number must be at least 10 digits';
    } else if (!/^\d{10}$/.test(formData.phone)) {
      newErrors.phone = 'Invalid phone number';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{6,}$/.test(formData.password)) {
      newErrors.password = 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character';
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    if (errors[e.target.name]) {
      setErrors({
        ...errors,
        [e.target.name]: ''
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (validateForm()) {
        setLoading(true);
        const { confirmPassword, ...dataToSend } = formData;
        console.log(dataToSend);
        const response = await postSignupData(dataToSend);
        console.log(response);
        if (response.data.success) {
          console.log("response");
          toast.success(response?.data?.message);
          navigate('/login');
        } else {
          toast.error(response?.data?.message);
        }
      }
    } catch (error) {
      toast.error(error?.response?.data?.errors || error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSuccess = async (credentialResponse) => {
    try {
      setLoading(true);
      const response = await googleAuth(credentialResponse.credential);

      if (response.data.success) {
        localStorage.setItem('token', response.data.token);
        setUser(response.data.user);

        if (response.data.restaurant) {
          setRestaurantData(response.data.restaurant);
        }

        toast.success(response.data.message);
        redirectBasedOnRole(response.data.user);
      } else {
        toast.error(response.data.message);
      }
    } catch (error) {
      toast.error(error?.response?.data?.message || 'Google authentication failed');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleError = () => {
    toast.error('Google sign in was unsuccessful');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FFF5E6] to-[#FFE5CC] py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="bg-white rounded-3xl shadow-2xl overflow-hidden">
          {/* Header */}
          <div className="px-6 py-8 text-center">
            <div className="flex items-center justify-center gap-2 mb-6">
              <svg className="w-10 h-10 text-[#EFA052]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v1m6 11h2m-6 0h-2m0 0H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h1 className="text-3xl font-bold text-[#EFA052]">Scan2Menu</h1>
            </div>
            <h2 className="text-2xl font-semibold text-gray-800">Create Your Account</h2>
            <p className="mt-2 text-gray-600">Get started with your restaurant's digital menu</p>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="px-6 pb-8">
            <div className="space-y-4">
              <InputField
                label="Name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                error={errors.name}
                placeholder="Enter your name"
                className="rounded-xl border-gray-300 focus:border-[#EFA052] focus:ring focus:ring-[#EFA052] focus:ring-opacity-50"
              />

              <InputField
                label="Username"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                error={errors.username}
                placeholder="Choose a username"
                className="rounded-xl border-gray-300 focus:border-[#EFA052] focus:ring focus:ring-[#EFA052] focus:ring-opacity-50"
              />

              <InputField
                label="Email Address"
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                error={errors.email}
                placeholder="Enter your email"
                className="rounded-xl border-gray-300 focus:border-[#EFA052] focus:ring focus:ring-[#EFA052] focus:ring-opacity-50"
              />

              <InputField
                label="Phone Number"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                error={errors.phone}
                maxLength={10}
                placeholder="Enter your phone number"
                className="rounded-xl border-gray-300 focus:border-[#EFA052] focus:ring focus:ring-[#EFA052] focus:ring-opacity-50"
              />

              <InputField
                label="Password"
                type="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                error={errors.password}
                placeholder="Create a password"
                className="rounded-xl border-gray-300 focus:border-[#EFA052] focus:ring focus:ring-[#EFA052] focus:ring-opacity-50"
              />

              <InputField
                label="Confirm Password"
                type="password"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                error={errors.confirmPassword}
                placeholder="Confirm your password"
                className="rounded-xl border-gray-300 focus:border-[#EFA052] focus:ring focus:ring-[#EFA052] focus:ring-opacity-50"
              />

              <Button
                type="submit"
                disabled={loading}
                className="w-full py-3 font-medium text-white bg-[#EFA052] hover:bg-[#D98F3B] rounded-xl transition-colors"
              >
                {loading ? (
                  <div className="flex items-center justify-center gap-2">
                    <Loader2 className="animate-spin" size={20} />
                    <span>Creating Account...</span>
                  </div>
                ) : (
                  'Sign Up'
                )}
              </Button>

              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">Or continue with</span>
                </div>
              </div>

              <div className="flex justify-center">
                <GoogleLogin
                  onSuccess={handleGoogleSuccess}
                  onError={handleGoogleError}
                  useOneTap
                />
              </div>
            </div>
          </form>

          <div className="px-6 pb-6 text-center">
            <p className="text-gray-600">
              Already have an account?{' '}
              <Link to="/login" className="text-[#EFA052] hover:text-[#D98F3B] font-semibold">
                Sign in
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUp;