const mongoose = require('mongoose');
const Restaurant = require('./restaurant.model');
const Subcategory = require('./item.model');
const Item = require('./item.model')

// Define the schema for Category
const categorySchema = new mongoose.Schema(
  {
    // Reference to the Restaurant this category belongs to
    restaurantId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Restaurant', // Refers to the Restaurant model
      required: true,
    },
    // Name of the category
    categoryName: {
      type: String,
      required: true
    },
    //array of the subcategory items
    subcategories: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Subcategory', // Refers to the Subcategory model
      },
    ],
    items: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Item', // Refers to the Item model
      },
    ],
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt fields
  }
);

// Middleware to handle cascade delete
categorySchema.pre('remove', async function (next) {
  try {
    // Get all subcategories linked to this category
    const subcategories = await Subcategory.find({ categoryId: this._id });
    
    // Delete all items in each subcategory
    for (const subcategory of subcategories) {
      await Item.deleteMany({ subcategoryId: subcategory._id });
    }

    // Delete all subcategories linked to this category
    await Subcategory.deleteMany({ categoryId: this._id });
    
    // Delete all items directly linked to this category
    await Item.deleteMany({ categoryId: this._id });
    
    next();
  } catch (err) {
    next(err);
  }
});

// Export the Category model
const Category = mongoose.model('Category', categorySchema);

module.exports = Category;
