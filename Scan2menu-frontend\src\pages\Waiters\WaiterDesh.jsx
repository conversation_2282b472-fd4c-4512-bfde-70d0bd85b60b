import React, { useEffect, useState, useContext } from 'react';
import { UserDataContext } from "../../context/UserContext";
import { Clock, Bell, User, CheckCircle, XCircle, List, DollarSign } from 'lucide-react';
import Navbar from './Navbar';
import { getAllWaiterOrders, updateStaffCheckInOut, getSingleUser } from '../../api';
import { Calendar, dateFnsLocalizer } from "react-big-calendar";
import { format, parse, startOfWeek, getDay, isValid } from "date-fns";
import enUS from "date-fns/locale/en-US";
import "react-big-calendar/lib/css/react-big-calendar.css";
// Calendar Component for current month
// const Calendar = () => {
//   const today = new Date();
//   const year = today.getFullYear();
//   const month = today.getMonth();
//   const firstDay = new Date(year, month, 1);
//   const lastDay = new Date(year, month + 1, 0);
//   const daysInMonth = lastDay.getDate();
//   const startDayIndex = firstDay.getDay();

//   // Create an array representing the calendar grid for the current month.
//   const calendarDays = [];
//   // Fill with empty slots until the first day of the month.
//   for (let i = 0; i < startDayIndex; i++) {
//     calendarDays.push(null);
//   }
//   for (let day = 1; day <= daysInMonth; day++) {
//     calendarDays.push(day);
//   }

//   // Dummy events for demonstration purposes.
//   const events = {
//     10: "Team Meeting",
//     15: "Lunch with Manager",
//     22: "Client Visit",
//   };

//   return (
//     <div className="bg-white p-6 rounded-lg shadow-md w-full">
//       <div className="text-xl font-semibold mb-4">
//         {today.toLocaleString('default', { month: 'long' })} {year}
//       </div>
//       <div className="grid grid-cols-7 gap-2 mb-4">
//         {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
//           <div key={index} className="text-center font-bold">
//             {day}
//           </div>
//         ))}
//       </div>
//       <div className="grid grid-cols-7 gap-2">
//         {calendarDays.map((day, index) => (
//           <div key={index} className="text-center p-4 border rounded relative h-20">
//             {day ? <span className="text-lg">{day}</span> : null}
//             {day && events[day] && (
//               <div className="bg-blue-500 text-white p-2 rounded absolute top-2 left-2 right-2 flex items-center justify-center">
//                 <Clock size={16} /> {events[day]}
//               </div>
//             )}
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// };



const WaiterDashboard = () => {
  const { user } = useContext(UserDataContext);
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [checkOutTime, setCheckOutTime] = useState(null);
  const [checkInTime, setCheckInTime] = useState(null);
  const [orderHistory, setOrderHistory] = useState([]);
  const [staffDetail, setStaffDetail] = useState(null);
  const [events, setEvents] = useState([]);

  const handleCheckInOut = async () => {
    try {
      const currentDateTime = new Date().toISOString();

      if (!isCheckedIn) {
        // Check-in
        setCheckInTime(currentDateTime);
        setIsCheckedIn(true);
        const response = await updateStaffCheckInOut(user._id, { start_time: currentDateTime });
        console.log(response);
      } else {
        // Check-out
        setCheckOutTime(currentDateTime);
        setIsCheckedIn(false);
        const response = await updateStaffCheckInOut(user._id, { end_time: currentDateTime });
        console.log(response);
      }
    } catch (error) {
      console.error("Attendance update failed:", error?.response?.data);
    }
  };
  const locales = { "en-US": enUS };
  const localizer = dateFnsLocalizer({ format, parse, startOfWeek, getDay, locales });

  // fetch waiter orders and staff details
  useEffect(() => {
    const fetchWatierOrders = async (id) => {
      try {
        const response = await getAllWaiterOrders(id);
        // console.log(response.data);
        setOrderHistory(response.data?.orders?.map((order) => ({
          _id: order._id,
          table: order.tableNumber,
          items: order.items.map((item) => item.itemId.title),
          total: order.total,
        })))

      } catch (error) {
        console.error(error);
      }
    }

    async function fetchUserStaffDetails(id) {
      try {
        const response = await getSingleUser(id);
        if (response.data.success) {
          setStaffDetail(response.data.staff);

          const attendanceRecords = response.data.staff.attendance;

          if (attendanceRecords.length > 0) {
            // const lastAttendance = attendanceRecords[attendanceRecords.length - 1];
            const today = new Date();
            const yesterday = new Date();
            yesterday.setDate(today.getDate() - 1);

            // Format to YYYY-MM-DD
            const todayStr = today.toISOString().split('T')[0];
            const yesterdayStr = yesterday.toISOString().split('T')[0];

            // Find yesterday's attendance
            const yesterdayAttendance = attendanceRecords.find(record => {
              const recordDate = new Date(record.date).toISOString().split('T')[0];
              return recordDate === yesterdayStr;
            });

            // Find today's attendance
            const todayAttendance = attendanceRecords.find(record => {
              const recordDate = new Date(record.date).toISOString().split('T')[0];
              return recordDate === todayStr;
            });

            // If yesterday’s attendance exists and has no end_time (meaning still checked in)
            if (yesterdayAttendance && yesterdayAttendance.start_time && !yesterdayAttendance.end_time) {
              setIsCheckedIn(true);
              setCheckInTime(yesterdayAttendance.start_time);
              setCheckOutTime(null);
            } else if (todayAttendance) {
              // If today’s attendance exists
              if (todayAttendance.start_time && !todayAttendance.end_time) {
                setIsCheckedIn(true);
                setCheckInTime(todayAttendance.start_time);
                setCheckOutTime(null);
              } else {
                setIsCheckedIn(false);
                setCheckInTime(todayAttendance.start_time);
                setCheckOutTime(todayAttendance.end_time);
              }
            } else {
              // No valid attendance found
              setIsCheckedIn(false);
              setCheckInTime(null);
              setCheckOutTime(null);
            }
          }
        }
      } catch (error) {
        console.error(error);
      }
    }

    if (user) {
      fetchWatierOrders(user._id)
      fetchUserStaffDetails(user._id)
    }
  }, [user]);

  // create events for the calendar
  useEffect(() => {
    if (staffDetail && staffDetail.attendance.length > 0) {
      const mappedEvents = staffDetail.attendance.map((record) => {
        const startDateTime = record.start_time
          ? new Date(record.start_time)
          : null;

        const endDateTime = record.end_time
          ? new Date(record.end_time)
          : null;
        return {
          title: record.presence,
          start: isValid(startDateTime) ? startDateTime : null,
          end: isValid(endDateTime) ? endDateTime : "",
        };
      });
      setEvents(mappedEvents);
    }
  }, [staffDetail]);
  useEffect(() => {
    console.log(events)
  }, [events])
  return (
    <div className="flex flex-col h-screen">
      <Navbar />

      <div className="flex-1 p-6 bg-gray-100">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <User size={32} />
            <span className="ml-2 text-lg">{user?.name}</span>
          </div>
          <div className="relative">
            <Bell size={24} />
            <span className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full px-1 text-xs">3</span>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">Daily Attendance</h2>

            {
              checkInTime && <p className="mb-4">
                Checked in at {format(new Date(checkInTime), 'hh:mm:ss a')}
              </p>
            }
            {
              checkOutTime && <p className="mb-4">
                Checked out at {format(new Date(checkOutTime), 'hh:mm:ss a')}
              </p>
            }
            {
              (!checkInTime || !checkOutTime) && <button
                onClick={handleCheckInOut}
                className="flex items-center justify-center bg-blue-500 text-white px-4 py-2 rounded"
              >
                {isCheckedIn ? <XCircle size={20} /> : <CheckCircle size={20} />}
                <span className="ml-2">{isCheckedIn ? 'Check Out' : 'Check In'}</span>
              </button>
            }

          </div>
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">Today's Summary</h2>
            <p>Tables Served: 15</p>
            <p>Total Earnings: $250</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">Total Serves</h2>
            <p>Total Orders: 30</p>
            <p>Total Items Served: 50</p>
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md mb-6">
          <h2 className="text-xl font-semibold mb-4">Today's Order History</h2>
          <div className="space-y-4">
            {orderHistory.map((order) => (
              <div key={order._id} className="border p-4 rounded">
                <h3 className="text-lg font-semibold">Table : {order.table}</h3>
                <p>Items: {order.items.join(', ')}</p>
                <p>Total: {order.total}</p>
              </div>
            ))}
          </div>
        </div>
        {/* <Calendar /> */}
        <div className='bg-white p-6 rounded-lg shadow-md w-full' >
          <Calendar
            localizer={localizer}
            events={events}
            startAccessor="start"
            endAccessor="end"
            style={{ height: 500 }}
            tooltipAccessor={(event) =>
              `Checked in: ${format(event.start, "hh:mm a")}  Checked out: ${event.end ? format(event.end, "hh:mm a") : "N/A"
              }`

            }
          />
        </div>
      </div>
    </div>
  );
};

export default WaiterDashboard;
