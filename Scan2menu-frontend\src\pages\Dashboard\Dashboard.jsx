import React, { useContext, useState, useEffect } from 'react';
import { UserDataContext } from '../../context/UserContext';
import { RestaurantDataContext } from '../../context/RestaurantContext';
import { useNavigate } from 'react-router-dom';
import QRCode from 'react-qr-code';
import { getDashboardData, getQRCodeSettings, getAllRestaurantStaff } from '../../api';
import { toast } from 'react-hot-toast';
const Dashboard = () => {
  const { user } = useContext(UserDataContext);
  const { restaurantData } = useContext(RestaurantDataContext);
  const navigate = useNavigate();

  // Initial defaults; note qrLink will be updated when restaurantData is loaded.
  const [qrSettings, setQrSettings] = useState({

    qrLink: import.meta.env.VITE_ENV === 'development' ? `http://192.168.0.147:5173/restaurant/${restaurantData?.slug}` : `https://scan-2-menu.netlify.app/restaurant/${restaurantData?.slug}`,
    displayText: restaurantData?.name || 'Restaurant Menu',
    foregroundColor: '#000000',
    backgroundColor: '#FFFFFF',
  });

  const [stats, setStats] = useState({
    totalOrders: 0,
    todayOrders: 0,
    todayEarnings: 0,
    activeMenuItems: 0,
  });

  const [recentOrders, setRecentOrders] = useState([]);

  const [activeWaiters, setActiveWaiters] = useState([]);

  // Remove localStorage retrieval and instead fetch from the database
  useEffect(() => {
    if (restaurantData) {
      // Compute the QR link based on the current environment and restaurant slug
      const computedQrLink =
        import.meta.env.VITE_ENV === 'development'
          ? `http://192.168.0.147:5173/restaurant/${restaurantData.slug}`
          : `https://scan-2-menu.netlify.app/restaurant/${restaurantData.slug}`;

      // Update the state with the computed QR link and default display text
      setQrSettings(prev => ({ ...prev, qrLink: computedQrLink, displayText: restaurantData.name || 'Restaurant Menu' }));

      // Fetch saved QR code settings from the database
      getQRCodeSettings(restaurantData._id)
        .then(response => {
          if (response.data && response.data.success) {
            const savedSettings = response.data.data.qrCodeSettings;
            // Merge saved settings with computed qrLink
            setQrSettings(prev => ({
              ...prev,
              ...savedSettings,
              qrLink: computedQrLink,
            }));
          }
        })
        .catch(error => {
          console.error('Error fetching QR Code settings:', error);
        });
    }
  }, [restaurantData]);

  useEffect(() => {
    // Fetch dashboard data
    const fetchDashboardData = async () => {
      if (!restaurantData) return;
      try {
        const response = await getDashboardData(restaurantData._id);
        const { data } = response.data;
        setStats({
          totalOrders: data.totalOrder,
          todayOrders: data.todayOrder,
          todayEarnings: data.todayEarnings,
          activeMenuItems: data.totalItem,
        });

        // Transform last3Order data
        const transformedOrders = data.last3Order.map(order => ({
          id: order._id,
          table: `Table ${order.tableNumber}`,
          items: order.items.map(item => item.itemId.title),
          total: `₹${order.total}`,
          status: order.status.charAt(0).toUpperCase() + order.status.slice(1),
        }));

        setRecentOrders(transformedOrders);

        // Add API call to fetch active waiters
        // const activeWaitersResponse = await getActiveWaiters(restaurantData._id);
        // setActiveWaiters(activeWaitersResponse.data);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      }
    };

    async function getRestaurantStaff(id) {
      try {
        const response = await getAllRestaurantStaff(id);
        if (response.data.success) {
          setActiveWaiters(
            response.data.data
              ?.filter((staf) => staf.status === "active")
              .map((staf) => ({
                _id: staf._id,
                name: staf.name,
                shift: staf?.shift || "",
              }))
          );
        }
      } catch (error) {
        toast.error(
          error.response?.data?.message ||
          "An error occurred. Please try again."
        );
      }
    }
    if (!restaurantData) return;
    getRestaurantStaff(restaurantData._id);
    fetchDashboardData();
  }, [restaurantData]);

  const notifications = [
    { id: 1, message: 'New order received for Table 5', time: '2 mins ago' },
    { id: 2, message: 'Menu item "Paneer Tikka" is low on stock', time: '10 mins ago' },
    { id: 3, message: 'Customer feedback received', time: '30 mins ago' },
  ];

  const upcomingEvents = [
    { id: 1, event: 'Live Music Night', date: 'Friday, 7 PM' },
    { id: 2, event: 'Happy Hour', date: 'Every Day, 5 PM - 7 PM' },
  ];

  // Navigation handlers
  const handleNavigation = (path) => {
    navigate(path);
  };

  // Update the Quick Actions array in the Dashboard component
  const quickActions = [
    { text: 'Add Waiter', path: '/waiters', gradient: 'from-[#7D1C4A] to-[#D17D98]' },
    { text: 'Add Menu Item', path: '/menu', gradient: 'from-green-400 to-green-600' },
    { text: 'View Orders', path: '/order', gradient: 'from-blue-400 to-blue-600' },
    { text: 'Generate QR', path: '/qr-code', gradient: 'from-purple-400 to-purple-600' },
    { text: 'Edit Profile', path: '/edit-profile', gradient: 'from-[#EFA052] to-[#D88A3B]' },
    {
      text: 'View Regular Menu',
      path: `restaurant/${restaurantData?.slug}`,
      gradient: 'from-indigo-400 to-indigo-600',
      isExternal: true
    },
    {
      text: 'View Normal Menu',
      path: `normalmenu/${restaurantData?.slug}`,
      gradient: 'from-pink-400 to-pink-600',
      isExternal: true
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Sidebar can be added here if needed */}

      <div className="p-8">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Header Section */}
          <div className="bg-white rounded-3xl shadow-xl p-8">
            <h1 className="text-4xl font-bold text-gray-800 mb-2">
              Welcome back, {user?.name || 'Restaurant Owner'}! 👋
            </h1>
            <p className="text-gray-600">
              Here's your restaurant's performance overview
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Stats cards with gradients */}
            <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium opacity-80">Total Orders</p>
                  <p className="text-3xl font-bold mt-1">{stats.totalOrders}</p>
                </div>
                <div className="bg-white/20 p-3 rounded-2xl">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-[#EFA052] to-[#D88A3B] rounded-xl shadow-xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium opacity-80">Today's Orders</p>
                  <p className="text-3xl font-bold mt-1">{stats.todayOrders}</p>
                </div>
                <div className="bg-white/20 p-3 rounded-2xl">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium opacity-80">Total Revenue</p>
                  <p className="text-3xl font-bold mt-1">₹{Number(stats.todayEarnings).toFixed(2)}</p>
                </div>
                <div className="bg-white/20 p-3 rounded-2xl">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium opacity-80">Menu Items</p>
                  <p className="text-3xl font-bold mt-1">{stats.activeMenuItems}</p>
                </div>
                <div className="bg-white/20 p-3 rounded-2xl">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Quick Actions Panel */}
            <div className="bg-white rounded-3xl shadow-xl p-6">
              <h2 className="text-2xl font-semibold text-gray-800 mb-6">Quick Actions</h2>
              <div className="space-y-4">
                {quickActions.map((action, index) => (
                  <button
                    key={index}
                    onClick={() => action.isExternal
                      ? window.open(`/${action.path}`, '_blank')
                      : handleNavigation(action.path)
                    }
                    className={`w-full p-4 bg-gradient-to-r ${action.gradient} text-white rounded-2xl 
                      hover:opacity-90 transition-all duration-300 shadow-md hover:shadow-lg 
                      transform hover:-translate-y-0.5 flex items-center justify-center gap-2`}
                  >
                    {action.text}
                    {action.isExternal && (
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                        />
                      </svg>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Recent Orders Panel */}
            <div className="bg-white rounded-3xl shadow-xl p-6 lg:col-span-2">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-semibold text-gray-800">Recent Orders</h2>
                <button
                  onClick={() => handleNavigation('/order')}
                  className="text-[#EFA052] hover:text-[#D88A3B] font-medium"
                >
                  View All →
                </button>
              </div>
              <div className="space-y-4">
                {recentOrders.map((order) => (
                  <div key={order.id}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-colors"
                  >
                    <div>
                      <p className="font-semibold text-gray-800">{order.table}</p>
                      <p className="text-sm text-gray-600">{order.items.join(', ')}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-gray-800">{order.total}</p>
                      <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${order.status === 'Completed' ? 'bg-green-100 text-green-800' :
                        order.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                        {order.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Bottom Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* QR Code Section */}
            <div className="bg-white rounded-3xl shadow-xl p-6">
              <h2 className="text-2xl font-semibold text-gray-800 mb-6">Quick Menu Access</h2>
              <div className="flex flex-col items-center space-y-6">
                <div className="bg-gray-50 p-6 rounded-2xl">
                  <QRCode
                    value={qrSettings.qrLink}
                    size={180}
                    level="H"
                    fgColor={qrSettings.foregroundColor}
                    bgColor={qrSettings.backgroundColor}
                  />
                </div>
                <button
                  onClick={() => navigate('/qr-code')}
                  className="w-full p-4 bg-gradient-to-r from-[#EFA052] to-[#D88A3B] text-white rounded-2xl hover:opacity-90 transition-all duration-300"
                >
                  Customize QR Code
                </button>
              </div>
            </div>

            {/* Notifications Section */}
            <div className="bg-white rounded-3xl shadow-xl p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-semibold text-gray-800">Notifications</h2>
                <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                  {notifications.length} New
                </span>
              </div>
              <div className="space-y-4">
                {notifications.map((notification) => (
                  <div key={notification.id} className="p-4 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-colors">
                    <p className="text-gray-800 font-medium">{notification.message}</p>
                    <p className="text-sm text-gray-500 mt-1">{notification.time}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Upcoming Events Section */}
            <div className="bg-white rounded-3xl shadow-xl p-6">
              <h2 className="text-2xl font-semibold text-gray-800 mb-6">Upcoming Events</h2>
              <div className="space-y-4">
                {upcomingEvents.map((event) => (
                  <div key={event.id} className="p-4 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-colors">
                    <p className="font-semibold text-gray-800">{event.event}</p>
                    <p className="text-sm text-gray-500 mt-1">{event.date}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Active Waiters Section */}
            <div className="bg-white rounded-3xl shadow-xl p-6">
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h2 className="text-2xl font-semibold text-gray-800">Today's Active Waiters</h2>
                  <p className="text-sm text-gray-500 mt-1">
                    {activeWaiters.length} waiters on duty
                  </p>
                </div>
                <button
                  onClick={() => handleNavigation('/waiters')}
                  className="text-[#EFA052] hover:text-[#D88A3B] font-medium flex items-center gap-1"
                >
                  Manage →
                </button>
              </div>

              <div className="space-y-4">
                {activeWaiters?.map((waiter) => (
                  <div
                    key={waiter?._id}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-2xl hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-[#EFA052] to-[#D88A3B] flex items-center justify-center text-white font-semibold">
                          {waiter?.name.charAt(0)}
                        </div>
                        <div className="absolute -bottom-1 -right-1 h-3 w-3 rounded-full bg-green-500 border-2 border-white"></div>
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-800">{waiter?.name}</h3>
                        <p className="text-xs text-gray-500">{waiter?.shift} Shift</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Active
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              {activeWaiters.length === 0 && (
                <div className="text-center py-8">
                  <div className="h-12 w-12 bg-gray-100 rounded-full mx-auto flex items-center justify-center mb-3">
                    <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <p className="text-gray-500">No active waiters at the moment</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
