const express = require('express');
const router = express.Router();
const menuController = require('../controllers/menu.controller')
const orderController = require('../controllers/order.controller');
const { authMiddeleware } = require('../middleware/auth.middleware');


router.get('/menu/:slug', menuController.getwholeMenu)

// Create a new order
router.post('/order', orderController.createOrder);

module.exports = router;