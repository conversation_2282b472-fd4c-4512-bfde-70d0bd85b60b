import React from 'react'
import { Navigate } from 'react-router-dom';

const CheckRole = ({ allowedRoles, children }) => {
    const user = JSON.parse(localStorage.getItem('user'));

    if (user && allowedRoles.includes(user.role)) {

        return children;
    } else {
        localStorage.removeItem('token');
        localStorage.removeItem('user');

        return <Navigate to="/login" />;
    }
};


export default CheckRole