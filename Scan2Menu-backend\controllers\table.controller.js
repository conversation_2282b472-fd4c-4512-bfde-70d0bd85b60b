const Table = require('../models/table.model');
const Restaurant = require('../models/restaurant.model');

// Get all tables for a restaurant
exports.getAllTables = async (req, res, next) => {
  try {
    const { restaurantSlug } = req.params;
    const restaurant = await Restaurant.findOne({ slug: restaurantSlug });
    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'Restaurant not found'
      });
    }
    const tables = await Table.find({ restaurantId: restaurant._id })
      .populate({
        path: 'currentOrder',
        select: 'name phoneNumber items status total'
      })
      .sort({ tableNumber: 1 });
    res.status(200).json({
      success: true,
      data: tables
    });
  } catch (error) {
    next(error);
  }
};

// Update table status when order is created
exports.bookTable = async (req, res, next) => {
  try {
    const { tableId } = req.params;
    const { orderId } = req.body;

    if (!orderId) {
      return res.status(400).json({
        success: false,
        message: 'Order ID is required'
      });
    }

    const table = await Table.findByIdAndUpdate(
      tableId,
      {
        status: 'occupied',
        currentOrder: orderId
      },
      { new: true }
    ).populate({
      path: 'currentOrder',
      select: 'name phoneNumber items status total'
    });

    if (!table) {
      return res.status(404).json({
        success: false,
        message: 'Table not found'
      });
    }

    // Emit socket event if socket instance exists
    // if (req.io) {
    //   req.io.emit('tableStatusChanged', table);
    // }

    res.status(200).json({
      success: true,
      data: table
    });
  } catch (error) {
    next(error);
  }
};

// Update table status (available/occupied/reserved)
exports.updateTableStatus = async (req, res, next) => {
  try {
    const { tableId } = req.params;
    const { status } = req.body;

    // Validate status
    if (!['available', 'occupied', 'reserved'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be available, occupied, or reserved'
      });
    }

    // If status is available, remove the current order
    const updateData = {
      status,
      ...(status === 'available' && { currentOrder: null })
    };

    const table = await Table.findByIdAndUpdate(
      tableId,
      updateData,
      { new: true }
    ).populate({
      path: 'currentOrder',
      select: 'name phoneNumber items status total'
    });

    if (!table) {
      return res.status(404).json({
        success: false,
        message: 'Table not found'
      });
    }

    // Emit socket event if socket instance exists
    if (req.io) {
      req.io.emit('tableStatusChanged', table);
    }

    res.status(200).json({
      success: true,
      data: table
    });
  } catch (error) {
    next(error);
  }
};

// Initialize tables for a restaurant
exports.initializeTables = async (req, res, next) => {
  try {
    const { restaurantId } = req.params;

    const restaurant = await Restaurant.findById(restaurantId);
    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'Restaurant not found'
      });
    }

    await Table.deleteMany({ restaurantId });

    const tables = Array.from({ length: restaurant.numberOfTables }, (_, i) => ({
      restaurantId,
      tableNumber: i + 1,
      status: 'available'
    }));

    const createdTables = await Table.insertMany(tables);

    // Check if Socket.IO instance exists before emitting
    if (req.io) {
      req.io.emit('tablesInitialized', createdTables);
    }

    res.status(201).json({
      success: true,
      data: createdTables
    });
  } catch (error) {
    next(error);
  }
}; 