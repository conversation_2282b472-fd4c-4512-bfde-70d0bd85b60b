import React, { useState, useEffect } from 'react';
import { RiCloseLine } from 'react-icons/ri';
import { Button } from '../ui/button';
import InputField from '../InputFieldValidation/InputField';
import { Loader } from 'lucide-react';

const AddExtraModal = ({ loading, setLoading, isOpen, onClose, onSave, editExtra = null }) => {

  const initialFormState = {
    title: '',
    price: ''
  };

  const [formData, setFormData] = useState(initialFormState);
  const [error, setError] = useState({});

  // Reset form when modal opens or editExtra changes
  useEffect(() => {
    if (isOpen) {
      if (editExtra) {
        setFormData({
          title: editExtra.title || '',
          price: editExtra.price || ''
        });
      } else {
        setFormData(initialFormState);
      }
      setError({});
    }
  }, [isOpen, editExtra]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));
    setError((prev) => ({ ...prev, [name]: '' }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const errors = {};
    if (!formData.title.trim()) errors.title = 'Title is required';
    if (!formData?.price) errors.price = 'Price is required';

    if (Object.keys(errors).length > 0) {
      setError(errors);
      return;
    }

    await onSave(formData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden">
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose} />

      <div className="relative bg-white rounded-xl shadow-xl w-full max-w-md mx-4 my-6">
        {/* Sticky Header */}
        <div className="sticky top-0 bg-white px-6 py-4 border-b border-gray-200 rounded-t-xl z-10">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-800">
              {editExtra ? 'Edit Extra' : 'Add New Extra'}
            </h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <RiCloseLine className="text-xl text-gray-500" />
            </button>
          </div>
        </div>

        {/* Scrollable Content */}
        <div className="px-6 py-4 max-h-[calc(100vh-12rem)] overflow-y-auto">
          <form onSubmit={handleSubmit} className="space-y-6">
            <InputField
              label="Extra Title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              error={error.title}
              placeholder="Enter extra title"
            />

            <InputField
              label="Price"
              name="price"
              type="number"
              value={formData.price}
              onChange={handleChange}
              error={error.price}
              placeholder="Enter price"
            />
          </form>
        </div>

        {/* Sticky Footer */}
        <div className="sticky bottom-0 bg-white px-6 py-4 border-t border-gray-200 rounded-b-xl">
          <Button
            type="submit"
            onClick={handleSubmit}
            className="w-full bg-gradient-to-r from-[#E28E66] to-[#F2A44F] hover:from-[#d68660] hover:to-[#e69847] text-white py-2 rounded-lg transition-colors"
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader className="h-4 w-4 animate-spin mr-2" />
                Please wait
              </>
            ) : (
              editExtra ? 'Update Extra' : 'Save Extra'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AddExtraModal;
