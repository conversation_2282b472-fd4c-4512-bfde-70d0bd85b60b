import React, { useState, createContext, useEffect } from 'react'

export const UserDataContext = createContext();
const UserContext = ({ children }) => {
    const [user, setUser] = useState(null);
    // Load user data from localStorage when the component mounts
    useEffect(() => {
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
            setUser(JSON.parse(storedUser)); // Parse and set the user data
        }
    }, []);

    // Save user data to localStorage whenever it changes
    useEffect(() => {
        if (user) {
            localStorage.setItem('user', JSON.stringify(user));
        } else {
            localStorage.removeItem('user'); // Remove user if set to null (e.g., on logout)
        }
    }, [user]);

    return (
        <>
            <UserDataContext.Provider value={{ user, setUser }}>
                {children}
            </UserDataContext.Provider>

        </>
    )
}

export default UserContext