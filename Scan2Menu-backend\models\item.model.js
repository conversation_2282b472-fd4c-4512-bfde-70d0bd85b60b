const mongoose = require('mongoose');
const Category = require('./category.model');
const Subcategory = require('./subcategory.model')


const ItemSchema = new mongoose.Schema(
  {
    // Reference to the Category this subcategory belongs to
    categoryId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Category', // Refers to the Category model
    },
    subcategoryId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Subcategory', // Refers to the Category model
    },
    // Title of the Item
    title: {
      type: String,
      required: true,
    },
    // Additional details about the Item
    detail: {
      type: String,
    },
    // Price associated with the Item
    price: {
      type: Number,
      required: true,
      min: 0, // Ensures the price cannot be negative
    },
    // Type of Item: veg or non-veg
    type: {
      type: String,
      enum: ['veg', 'non-veg'], // Restricts value to 'veg' or 'non-veg'
      required: true,
    },
    // URL of the subcategory image
    subimage: {
      type: String, // Will store the image URL (e.g., from Cloudinary)
      required: true,
    },
    // Indicates if the menu button is available (1: available, 0: not available)
    menuButton: {
      type: Boolean,
      enum: [true, false],
      default: 0, // Default is not available
    },
    // Array of extras associated with this subcategory
    extras: [
      {
        // Title of the extra item
        title: {
          type: String,
          required: true,
        },
        // Price of the extra item
        price: {
          type: Number,
          required: true,
          min: 0, // Ensures the price cannot be negative
        },
      },
    ],
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt fields
  }
);

// Export the Subcategory model
const Item = mongoose.model('Item', ItemSchema);
module.exports = Item;
