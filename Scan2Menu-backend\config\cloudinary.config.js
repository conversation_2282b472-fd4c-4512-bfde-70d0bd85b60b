const cloudinary = require('cloudinary').v2;

// Cloudinary configuration
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

exports.uploadToCloudinary = async (imageData, folderPath) => {
  try {
    const result = await cloudinary.uploader.upload(imageData, {
      folder: folderPath,
    });
    return result;
  } catch (err) {
    throw new Error('Error uploading to Cloudinary: ' + err.message);
  }
};

module.exports = cloudinary;
