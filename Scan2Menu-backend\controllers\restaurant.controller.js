const Restaurant = require('../models/restaurant.model');
const User = require('../models/user.model');
const Category = require('../models/category.model');
const Subcategory = require('../models/subcategory.model');
const Item = require('../models/item.model');
const Order = require('../models/order.model');
const { restaurantValidationSchema } = require('../validators/restaurantValidator');
const cloudinary = require('../config/cloudinary.config');
const { extractPublicIdFromUrl } = require('../utils/helper');
const mongoose = require('mongoose');
const Table = require('../models/table.model');

// Create a New Restaurant
exports.createRestaurant = async (req, res, next) => {
  try {
    // Validate input data using Joi
    const { error } = restaurantValidationSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ message: error.details[0].message, success: false });
    }

    const { name, slug, ...otherFields } = req.body;


    // Check if the slug already exists
    const existingSlug = await Restaurant.findOne({ slug });
    if (existingSlug) {
      return res.status(400).json({ message: 'Slug already exists. Please choose a unique one.', success: false });
    }

    // Extract Cloudinary URLs from uploaded files
    const image = req.files?.image ? req.files.image[0].path : null; // Cloudinary URL for image
    const coverImage = req.files?.coverImage ? req.files.coverImage[0].path : null; // Cloudinary URL for cover image

    // Create the new restaurant
    const userId = req.user._id;
    const newRestaurant = new Restaurant({
      name,
      slug,
      userId,
      image,
      coverImage,
      ...otherFields,
    });

    await newRestaurant.save();

    // Create tables for the restaurant
    const tables = Array.from({ length: newRestaurant.numberOfTables }, (_, i) => ({
      restaurantId: newRestaurant._id,
      tableNumber: i + 1,
      status: 'available'
    }));

    await Table.insertMany(tables);

    res.status(201).json({
      message: 'Restaurant created successfully',
      restaurant: newRestaurant,
      success: true
    });
  } catch (err) {
    next(err);
  }
};

// Update an Existing Restaurant
exports.updateRestaurant = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = null;

    const { error } = restaurantValidationSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ message: error.details[0].message, success: false });
    }

    const { ...updateFields } = req.body;


    // Ensure userId is not accidentally updated
    if (userId) {
      return res.status(400).json({ message: 'userId cannot be updated.', success: false });
    }

    // Check if the restaurant exists
    const restaurant = await Restaurant.findById(id);
    if (!restaurant) {
      return res.status(404).json({ message: 'Restaurant not found', success: false });
    }

    if (restaurant.numberOfTables != updateFields.numberOfTables) {
      await Table.deleteMany({ restaurantId: id });

      let tables = Array.from({ length: updateFields.numberOfTables }, (_, i) => ({
        restaurantId: id,
        tableNumber: i + 1,
        status: 'available'
      }));
      await Table.insertMany(tables);
    }
    // Handle image updates
    if (req.files?.image) {
      updateFields.image = req.files.image[0].path; // Cloudinary URL for new image
    }

    if (req.files?.coverImage) {
      updateFields.coverImage = req.files.coverImage[0].path; // Cloudinary URL for new cover image
    }

    // Update the restaurant
    const updatedRestaurant = await Restaurant.findByIdAndUpdate(
      id,
      { $set: { ...updateFields, slug: restaurant.slug } }, // Preserve existing slug
      { new: true }
    );

    res.status(200).json({ message: 'Restaurant updated successfully', restaurant: updatedRestaurant, success: true });
  } catch (err) {
    next(err);
  }
};

// Get All Restaurants
exports.getAllRestaurants = async (req, res, next) => {
  try {
    const restaurants = await Restaurant.find();
    res.status(200).json(restaurants);
  } catch (err) {
    next(err);
  }
};

// Get Restaurant by ID or Slug
exports.getRestaurant = async (req, res, next) => {
  try {
    const { param } = req.params;

    const restaurant = await Restaurant.findOne({
      $or: [{ _id: param }, { slug: param }],
    });

    if (!restaurant) {
      return res.status(404).json({ message: 'Restaurant not found' });
    }

    res.status(200).json(restaurant);
  } catch (err) {
    next(err);
  }
};

// Delete a Restaurant
exports.deleteRestaurant = async (req, res, next) => {
  try {
    const { param } = req.params;

    // Find the restaurant by ID or slug
    const restaurant = await Restaurant.findOneAndDelete({
      $or: [{ _id: param }, { slug: param }],
    });

    if (!restaurant) {
      return res.status(404).json({ message: 'Restaurant not found', success: false });
    }

    // Delete the associated images from Cloudinary
    const deleteImagePromises = [];
    if (restaurant.image) {
      const publicId = extractPublicIdFromUrl(restaurant.image);
      if (publicId) deleteImagePromises.push(cloudinary.uploader.destroy(publicId));
    }
    if (restaurant.coverImage) {
      const publicId = extractPublicIdFromUrl(restaurant.coverImage);
      if (publicId) deleteImagePromises.push(cloudinary.uploader.destroy(publicId));
    }

    // Wait for all image deletions to complete
    await Promise.all(deleteImagePromises);

    res.status(200).json({
      message: 'Restaurant deleted successfully',
      success: true,
    });
  } catch (err) {
    next(err);
  }
};

exports.dashboard = async (req, res, next) => {
  try {
    const { id } = req.params;
    const restaurantId = id;

    // Validate restaurantId
    if (!restaurantId) {
      return res.status(400).json({
        message: 'Restaurant ID is required',
        success: false
      });
    }

    // Validate if restaurant exists
    const restaurant = await Restaurant.findById(restaurantId);
    if (!restaurant) {
      return res.status(404).json({
        message: 'Restaurant not found',
        success: false
      });
    }

    //total number of order
    const totalOrder = await Order.countDocuments({ restaurantId }).catch(err => {
      throw new Error('Error getting total orders: ' + err.message);
    });

    //last 3order
    const last3Order = await Order.find({ restaurantId })
      .populate({
        path: 'items.itemId',
        select: 'title'
      })
      .sort({ createdAt: -1 })
      .limit(3)
      .catch(err => {
        throw new Error('Error getting recent orders: ' + err.message);
      });

    //today's order    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayOrder = await Order.countDocuments({
      restaurantId,
      createdAt: { $gte: today }
    }).catch(err => {
      throw new Error('Error getting today\'s orders: ' + err.message);
    });

    //total Item
    const categories = await Category.find({ restaurantId }).catch(err => {
      throw new Error('Error getting categories: ' + err.message);
    });

    if (!categories.length) {
      return res.status(200).json({
        data: {
          totalOrder,
          last3Order,
          todayOrder,
          totalItem: 0,
          totalRevenue: 0
        },
        success: true
      });
    }

    const categoryIds = categories.map(category => category._id);

    const subcategories = await Subcategory.find({
      categoryId: { $in: categoryIds }
    }).distinct('_id').catch(err => {
      throw new Error('Error getting subcategories: ' + err.message);
    });

    const totalItem = await Item.countDocuments({
      $or: [
        { categoryId: { $in: categoryIds } },
        { subcategoryId: { $in: subcategories } }
      ]
    }).catch(err => {
      throw new Error('Error getting total items: ' + err.message);
    });

    //total revenue
    const Revenue = await Order.aggregate([
      { $match: { restaurantId: new mongoose.Types.ObjectId(restaurantId) } },
      { $group: { _id: null, total: { $sum: "$total" } } }
    ]).catch(err => {
      throw new Error('Error calculating total revenue: ' + err.message);
    });

    const totalRevenue = Revenue.length > 0 ? Revenue[0].total : 0;

    // Calculate today's revenue
    const todayRevenue = await Order.aggregate([
      {
        $match: {
          restaurantId: new mongoose.Types.ObjectId(restaurantId),
          createdAt: { $gte: today }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: "$total" }
        }
      }
    ]).catch(err => {
      throw new Error('Error calculating today\'s revenue: ' + err.message);
    });

    const todayEarnings = todayRevenue.length > 0 ? todayRevenue[0].total : 0;

    const data = {
      totalOrder,
      last3Order,
      todayOrder,
      totalItem,
      totalRevenue,
      todayEarnings // Add this new field
    }

    return res.status(200).json({ data, success: true });

  } catch (error) {
    next(error)
  }
}