const express = require('express');
const router = express.Router();
const userRoute = require("./users.route");
const restaurantRoute = require("./restaurant.route");
const categoryRoute = require("./category.route");
const subcategoryRoute = require("./subcategory.route");
const itemRoute = require('./item.route');
const menuRoute = require('./menu.router');
const orderRoute = require('./order.route')
const { authMiddeleware } = require('../middleware/auth.middleware');
const tableRoutes = require('./table.route');
const qrCodeRoute = require('./qrcodesettings.router');
const paymentRoute = require('./payment.route');

//redirecting to user routes
router.use("/users", userRoute);
router.use("/restaurant", authMiddeleware, restaurantRoute);
router.use("/category", authMiddeleware, categoryRoute);
router.use("/subcategory", authMiddeleware, subcategoryRoute);
router.use("/item", authMiddeleware, itemRoute)
router.use("/order", authMiddeleware, orderRoute);
router.use("/qrcode", authMiddeleware, qrCodeRoute);
router.use("/u", menuRoute);
router.use("/tables", tableRoutes);
router.use("/payment", paymentRoute); // Payment routes don't need auth middleware for customer payments

module.exports = router;
