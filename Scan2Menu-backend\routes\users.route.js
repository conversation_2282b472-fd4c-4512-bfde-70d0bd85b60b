const express = require('express');
const router = express.Router();
const userController = require("../controllers/users.controller");
const { authMiddeleware } = require("../middleware/auth.middleware");

// Google Authentication route (should be before protected routes)
router.post('/auth/google', userController.googleAuth);

/* GET all users */
router.get("/", authMiddeleware, userController.getAllUsers);

/* Logout a user */
router.post("/logout", authMiddeleware, userController.logoutUser);

/* GET a single user by ID, username, or email */
router.get("/:param", authMiddeleware, userController.getSingleUser);

/* Create a new user */
router.post("/signup", userController.createUser);

/* Login a user */
router.post("/login", userController.loginUser);

/* Update a user by ID */
router.put("/:param", authMiddeleware, userController.updateUser);

/* Delete a user by ID, username, or email */
router.delete("/:param", authMiddeleware, userController.deleteUser);

//for the forgot password page to redirect and send mail
router.post('/forgot-password', userController.forgotPassword);

// for the reset password page to reenter te password
router.post('/reset-password/:token', userController.resetPassword);

/* GET all restaurants staf */
router.get("/restaurant/staf/:id", authMiddeleware, userController.getAllRestaurantStaff);

/* Updata  Staff Checkin-out */
router.post("/restaurant/staf/checkinout/:id", authMiddeleware, userController.updateStaffCheckInOut);
module.exports = router;