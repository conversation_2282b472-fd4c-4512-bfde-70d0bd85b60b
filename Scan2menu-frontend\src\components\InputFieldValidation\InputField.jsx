import React from 'react';

const InputField = ({
  label,
  name,
  value,
  onChange,
  error,
  placeholder,
  type = 'text',
  className = '',
  multiline = false,
  rows = 4,
  disabled = false,
  ...props
}) => {
  const inputClasses = `
    w-full
    px-4
    py-3
    border-2
    border-gray-200
    rounded-lg
    focus:border-[#E28E66]
    focus:ring-2
    focus:ring-[#E28E66]/20
    outline-none
    transition-all
    duration-200
    ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : ''}
    ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
    ${className}
  `;

  return (
    <div className="space-y-2">
      {label && (
        <label htmlFor={name} className="block text-sm font-medium text-gray-700">
          {label}
        </label>
      )}

      {multiline ? (
        <textarea
          id={name}
          name={name}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          rows={rows}
          disabled={disabled}
          className={inputClasses}
          {...props}
        />
      ) : (
        <input
          type={type}
          id={name}
          name={name}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          disabled={disabled}
          className={inputClasses}
          {...props}
        />
      )}

      {error && (
        <p className="text-sm text-red-500 ">{error}</p>
      )}
    </div>
  );
};

export default InputField;
