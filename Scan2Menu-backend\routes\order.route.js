const express = require('express');
const router = express.Router();
const orderController = require('../controllers/order.controller');




// Get an single order by order ID
router.get('/s/:orderId', orderController.getOrderById);

// Create a new order
router.post('/waiter', orderController.createOrder);

// Update an order
router.put('/:orderId', orderController.updateOrderStatus);

// Delete an order
router.delete('/d/:orderId', orderController.deleteOrder);

// Get orders by date filter
router.get('/filter/:restaurantId', orderController.getOrdersByFilter);

// Get all orders by restaurant ID
router.get('/:restaurantId', orderController.getOrdersByRestaurantId);

//Get all orders of waiter
router.get('/waiter/:id', orderController.getOrdersByWaiter);
module.exports = router;
