import React, { useState, useEffect, useContext } from 'react';
import { format } from 'date-fns';
import { getAllOrders, deleteOrder, updateOrderStatus, getOrdersByFilter } from '../../api';
import { RestaurantDataContext } from '../../context/RestaurantContext';
import { toast } from 'react-hot-toast';
import { Loader2, Eye, Trash2, Calendar, Search, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import ViewOrderModal from '../../components/Modal/ViewOrderModal';

const Order = () => {
  const { restaurantData } = useContext(RestaurantDataContext);
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [limit, setLimit] = useState(10); // Items per page
  const [isMobile, setIsMobile] = useState(window.innerWidth < 640);
  const [selectedOrder, setSelectedOrder] = useState(null); // For modal view
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null); // For delete confirmation
  const [showAllOrderItems, setShowAllOrderItems] = useState(false);
  // Page size options
  const pageSizeOptions = [5, 10, 20, 50, 100];

  // Handle page size change
  const handlePageSizeChange = (newLimit) => {
    setLimit(Number(newLimit));
    setCurrentPage(1);
  };

  const fetchOrders = async () => {
    try {
      setLoading(true);
      let response;
      if (selectedDate) {
        const formattedDate = format(new Date(selectedDate), 'dd-MM-yyyy');
        response = await getOrdersByFilter(
          restaurantData._id,
          formattedDate,
          currentPage,
          limit
        );
      } else {
        response = await getAllOrders(restaurantData._id, currentPage, limit);
      }
      if (response.data.success) {
        console.log(response)
        setOrders(response.data.orders);
        setTotalPages(response.data.pagination.pages);
      }
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to fetch orders');
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (restaurantData?._id) {
      fetchOrders();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [restaurantData, currentPage, selectedDate, limit]);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 640);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleDateChange = (e) => {
    setSelectedDate(e.target.value);
    setCurrentPage(1);
  };

  const handleDeleteOrder = async (orderId) => {
    try {
      const response = await deleteOrder(orderId);
      if (response.data.success) {
        toast.success('Order deleted successfully');
        setOrders(orders.filter(order => order._id !== orderId));
      }
      setShowDeleteConfirm(null);
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to delete order');
    }
  };

  const handleStatusChange = async (orderId, status) => {
    try {
      if (status === 'completed' || status === 'cancelled') {
        const confirm = window.confirm(`Are you sure you want to mark this order as ${status}?`);
        if (!confirm) {
          return;
        }
      }
      const response = await updateOrderStatus(orderId, status);
      if (response.data.success) {
        toast.success('Order status updated successfully');
        setOrders(orders.map(order =>
          order._id === orderId ? response.data.order : order
        ));
      }
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to update order status');
    }
  };

  const handleViewOrder = (order) => {
    setSelectedOrder(order);
  };

  const handleCloseModal = () => {
    setSelectedOrder(null);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'preparing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'served':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusStyles = (status) => {
    const baseStyles =
      'rounded-full px-3 py-1 text-sm font-medium border transition-colors duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-offset-2';
    switch (status) {
      case 'completed':
        return `${baseStyles} ${getStatusColor(status)} hover:bg-green-200 focus:ring-green-500`;
      case 'preparing':
        return `${baseStyles} ${getStatusColor(status)} hover:bg-blue-200 focus:ring-blue-500`;
      case 'served':
        return `${baseStyles} ${getStatusColor(status)} hover:bg-purple-200 focus:ring-purple-500`;
      case 'cancelled':
        return `${baseStyles} ${getStatusColor(status)} hover:bg-red-200 focus:ring-red-500`;
      case 'pending':
        return `${baseStyles} ${getStatusColor(status)} hover:bg-yellow-200 focus:ring-yellow-500`;
      default:
        return `${baseStyles} ${getStatusColor(status)} hover:bg-gray-200 focus:ring-gray-500`;
    }
  };

  const PaginationControls = () => (
    <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mt-4 bg-white p-4 rounded-lg shadow-sm border border-gray-200">
      {/* Page Size Selector */}
      <div className="flex items-center gap-2">
        <label htmlFor="pageSize" className="text-sm text-gray-600 whitespace-nowrap">
          Show:
        </label>
        <select
          id="pageSize"
          value={limit}
          onChange={(e) => handlePageSizeChange(e.target.value)}
          className="rounded-md border border-gray-300 px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50 bg-white text-gray-700"
        >
          {pageSizeOptions.map(size => (
            <option key={size} value={size}>
              {size} entries
            </option>
          ))}
        </select>
      </div>
      {/* Pagination */}
      <div className="flex items-center gap-1">
        <button
          onClick={() => setCurrentPage(1)}
          disabled={currentPage === 1}
          className="flex items-center justify-center w-8 h-8 text-gray-600 hover:bg-gray-100 rounded-md disabled:opacity-50 disabled:hover:bg-transparent"
        >
          <ChevronsLeft size={16} />
        </button>
        <button
          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
          disabled={currentPage === 1}
          className="flex items-center justify-center w-8 h-8 text-gray-600 hover:bg-gray-100 rounded-md disabled:opacity-50 disabled:hover:bg-transparent"
        >
          <ChevronLeft size={16} />
        </button>
        <div className="flex items-center gap-1 px-2">
          <span className="text-sm text-gray-700">
            Page {currentPage} of {totalPages}
          </span>
        </div>
        <button
          onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
          disabled={currentPage === totalPages || totalPages === 0}
          className="flex items-center justify-center w-8 h-8 text-gray-600 hover:bg-gray-100 rounded-md disabled:opacity-50 disabled:hover:bg-transparent"
        >
          <ChevronRight size={16} />
        </button>
        <button
          onClick={() => setCurrentPage(totalPages)}
          disabled={currentPage === totalPages || totalPages === 0}
          className="flex items-center justify-center w-8 h-8 text-gray-600 hover:bg-gray-100 rounded-md disabled:opacity-50 disabled:hover:bg-transparent"
        >
          <ChevronsRight size={16} />
        </button>
      </div>
    </div>
  );

  // Delete confirmation modal
  const DeleteConfirmModal = ({ orderId, onCancel }) => (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-xl shadow-xl p-6 max-w-sm w-full mx-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">Confirm Delete</h3>
        <p className="text-gray-600 mb-6">Are you sure you want to delete this order? This action cannot be undone.</p>
        <div className="flex justify-end gap-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={() => handleDeleteOrder(orderId)}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition-colors"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );

  // Empty state component
  const EmptyState = () => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 flex flex-col items-center justify-center text-center">
      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <Search className="w-8 h-8 text-gray-400" />
      </div>
      <h3 className="text-lg font-semibold text-gray-800 mb-2">No Orders Found</h3>
      <p className="text-gray-500 mb-4 max-w-md">
        {selectedDate
          ? `No orders found for the selected date. Try a different date or clear the date filter.`
          : `There are no orders in the system yet. Orders will appear here once customers place them.`
        }
      </p>
      {selectedDate && (
        <button
          onClick={() => setSelectedDate('')}
          className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
        >
          Clear Date Filter
        </button>
      )}
    </div>
  );

  return (
    <div className="w-full px-4 space-y-6 pb-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <h1 className="text-2xl font-bold text-gray-800">Orders</h1>
        <div className="w-full sm:w-auto flex items-center gap-2 border border-gray-300 rounded-lg overflow-hidden bg-white pr-2">
          <div className="flex items-center bg-gray-50 border-r border-gray-300 px-3 py-2">
            <Calendar className="w-5 h-5 text-gray-500" />
          </div>
          <input
            type="date"
            value={selectedDate}
            onChange={handleDateChange}
            className="w-full border-none focus:ring-0 py-2 px-2 text-gray-700 flex-grow"
            placeholder="Filter by date..."
          />
          {selectedDate && (
            <button
              onClick={() => setSelectedDate('')}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
            </button>
          )}
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64 bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="flex flex-col items-center">
            <Loader2 className="h-10 w-10 animate-spin text-blue-500 mb-2" />
            <p className="text-gray-500">Loading orders...</p>
          </div>
        </div>
      ) : orders.length === 0 ? (
        <EmptyState />
      ) : (
        <>
          {isMobile ? (
            // Mobile Card View
            <div className="space-y-4 text-sm">
              {orders.map((order) => (
                <div
                  key={order._id}
                  className="bg-white rounded-xl shadow-md border border-gray-200 overflow-hidden"
                >
                  {/* Card Header */}
                  <div className="p-4 border-b border-gray-200">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="text-base font-semibold text-gray-800">
                          #{order._id.slice(-6).toUpperCase()}
                        </h3>
                        <div className="mt-1">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Table {order.tableNumber}
                          </span>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">{order.name || 'Not provided'}</p>
                      </div>
                      <select
                        value={order.status}
                        onChange={(e) => handleStatusChange(order._id, e.target.value)}
                        className={getStatusStyles(order.status)}
                      >
                        <option className={getStatusStyles('pending')} value="pending" disabled={order.status === "preparing" || order.status === "completed" || order.status === 'cancelled'}>Pending</option>
                        <option className={getStatusStyles('preparing')} value="preparing" disabled={order.status == 'completed' || order.status == 'served' || order.status === 'cancelled'} >Preparing</option>
                        <option className={getStatusStyles('served')} value="served" disabled={order.status == 'completed' || order.status === 'cancelled'}>Served</option>
                        <option className={getStatusStyles('completed')} value="completed" disabled={order.status == 'cancelled'}>Completed</option>
                        <option className={getStatusStyles('cancelled')} value="cancelled" disabled={order.status == 'completed'}>Cancelled</option>
                      </select>
                    </div>
                    <p className="text-xs text-gray-500">
                      {format(new Date(order.createdAt), 'dd/MM/yyyy hh:mm a')}
                    </p>
                  </div>

                  {/* Order Items */}
                  <div className="p-4 bg-gray-50 border-b border-gray-200">
                    <p className="text-sm font-medium text-gray-600 mb-2">Order Items</p>

                    <div className="space-y-2">
                      {order.items?.slice(0, showAllOrderItems ? order.item?.length : 3).map((item, idx) => (
                        <div key={idx}>
                          <div className="flex items-center gap-2 text-gray-600">
                            <span className="text-green-500">🍽️</span>
                            <span className="font-medium text-gray-700">{item.itemId?.title || 'Unknown Item'}</span>
                            <span className="text-xs px-2 py-0.5 bg-gray-100 rounded-full text-gray-600">× {item.quantity}</span>
                          </div>
                          {item.selectedExtras.length > 0 && (<span className='text-gray-400'>
                            ( {
                              item.selectedExtras.map((extra) => (

                                <span key={extra._id} >{extra.title},</span>
                              ))
                            })
                          </span>
                          )}
                        </div>


                      ))}
                      {order.items?.length > 3 && (
                        <p onClick={() => setShowAllOrderItems(!showAllOrderItems)} className="text-xs text-gray-500 italic cursor-pointer">
                          {showAllOrderItems ? "Show Less" : `+${order.items.length - 3} more items`}
                        </p>
                      )}
                    </div>

                  </div>

                  {/* Card Footer */}
                  <div className="p-4 flex justify-between items-center bg-white">
                    <p className="text-lg font-semibold text-gray-800">
                      ₹{order.total?.toFixed(2) || '0.00'}
                    </p>
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleViewOrder(order)}
                        className="text-blue-500 hover:text-blue-600 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                      >
                        <Eye size={18} />
                      </button>
                      <button
                        onClick={() => setShowDeleteConfirm(order._id)}
                        className="text-red-500 hover:text-red-600 p-2 rounded-lg hover:bg-red-50 transition-colors"
                      >
                        <Trash2 size={18} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            // Desktop Table View
            <div className="rounded-xl border border-gray-200 shadow-md overflow-hidden bg-white">
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gray-800 text-white text-sm">
                    <tr>
                      <th className="px-4 py-3 text-left font-semibold w-12 text-center">#</th>
                      <th className="px-4 py-3 text-left font-semibold w-8">Order ID</th>
                      <th className="px-4 py-3 text-left font-semibold w-14">Table</th>
                      <th className="px-4 py-3 text-left font-semibold">Menu Items</th>
                      <th className="px-4 py-3 text-left font-semibold w-24">Customer</th>
                      <th className="px-4 py-3 text-left font-semibold w-[10%]">Total</th>
                      <th className="px-4 py-3 text-center font-semibold">Status</th>
                      <th className="px-4 py-3 text-left font-semibold">Time</th>
                      <th className="px-4 py-3 text-center font-semibold">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {orders.map((order, index) => (
                      <tr key={order._id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-4 py-3 whitespace-normal break-words text-center text-gray-500">
                          {(currentPage - 1) * limit + index + 1}
                        </td>
                        <td className="px-4 py-3 whitespace-normal break-words font-medium">
                          #...{order._id.toString().slice(-6)}
                        </td>
                        <td className="px-4 py-3 whitespace-normal break-words">
                          <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {order.tableNumber}
                          </div>
                        </td>
                        <td className="px-4 py-3 whitespace-normal break-words">
                          <div className="space-y-1.5">
                            {order.items?.slice(0, showAllOrderItems ? order.item?.length : 3).map((item, idx) => (
                              <div key={idx}>
                                <div className="flex items-center gap-2 text-gray-600">
                                  <span className="text-green-500">🍽️</span>
                                  <span className="font-medium text-gray-700">{item.itemId?.title || 'Unknown Item'}</span>
                                  <span className="text-xs px-2 py-0.5 bg-gray-100 rounded-full text-gray-600">× {item.quantity}</span>
                                </div>
                                {item.selectedExtras.length > 0 && (<span className='text-gray-400'>
                                  ( {
                                    item.selectedExtras.map((extra) => (

                                      <span key={extra._id} >{extra.title},</span>
                                    ))
                                  })
                                </span>
                                )}
                              </div>


                            ))}
                            {order.items?.length > 3 && (
                              <p onClick={() => setShowAllOrderItems(!showAllOrderItems)} className="text-xs text-gray-500 italic cursor-pointer">
                                {showAllOrderItems ? "Show Less" : `+${order.items.length - 3} more items`}
                              </p>
                            )}
                          </div>


                        </td>
                        <td className="px-4 py-3 whitespace-normal break-words text-gray-600">
                          {order.name || 'Not provided'}
                        </td>
                        <td className="px-4 py-3 whitespace-normal break-words font-medium">
                          ₹{order.total?.toFixed(2) || '0.00'}
                        </td>
                        <td className="px-4 py-3 whitespace-normal break-words text-center">
                          <select
                            value={order.status}
                            onChange={(e) => handleStatusChange(order._id, e.target.value)}
                            className={getStatusStyles(order.status)}
                          >
                            <option className={getStatusStyles('pending')} value="pending" disabled={order.status === "preparing" || order.status === "completed" || order.status === 'cancelled'}>Pending</option>
                            <option className={getStatusStyles('preparing')} value="preparing" disabled={order.status == 'completed' || order.status == 'served' || order.status === 'cancelled'} >Preparing</option>
                            <option className={getStatusStyles('served')} value="served" disabled={order.status == 'completed' || order.status === 'cancelled'}>Served</option>
                            <option className={getStatusStyles('completed')} value="completed" disabled={order.status == 'cancelled'}>Completed</option>
                            <option className={getStatusStyles('cancelled')} value="cancelled" disabled={order.status == 'completed'}>Cancelled</option>
                          </select>
                        </td>
                        <td className="px-4 py-3 whitespace-normal break-words text-gray-500">
                          {format(new Date(order.createdAt), 'dd/MM/yyyy hh:mm a')}
                        </td>
                        <td className="px-4 py-3 whitespace-normal break-words">
                          <div className="flex gap-2 justify-center">
                            <button
                              onClick={() => handleViewOrder(order)}
                              className="p-2 rounded-lg text-blue-500 hover:bg-blue-50 hover:text-blue-600 transition-colors"
                              title="View Order"
                            >
                              <Eye size={18} />
                            </button>
                            <button
                              onClick={() => setShowDeleteConfirm(order._id)}
                              className="p-2 rounded-lg text-red-500 hover:bg-red-50 hover:text-red-600 transition-colors"
                              title="Delete Order"
                            >
                              <Trash2 size={18} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Pagination Controls */}
          {!loading && orders.length > 0 && <PaginationControls />}
        </>
      )}

      {/* View Order Modal */}
      {selectedOrder && (
        <ViewOrderModal order={selectedOrder} onClose={handleCloseModal} />
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <DeleteConfirmModal
          orderId={showDeleteConfirm}
          onCancel={() => setShowDeleteConfirm(null)}
        />
      )}
    </div>
  );
};

export default Order;