import React, { useContext } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { jwtDecode } from 'jwt-decode';


const isTokenValid = (token) => {
    try {
        const decoded = jwtDecode(token);
        return decoded.exp * 1000 > Date.now();
    } catch (error) {
        console.error("protected error", error);
        return false;
    }
};

const ProtectedRoute = ({ children }) => {

    const location = useLocation();

    const token = localStorage.getItem('token');
    const user = JSON.parse(localStorage.getItem('user'));
    if (!token || !isTokenValid(token) || !user) {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('restaurantData');
        return <Navigate to="/login" state={{ from: location }} />;
    }

    return children;
};

export default ProtectedRoute;
