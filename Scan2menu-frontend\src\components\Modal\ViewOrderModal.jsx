import { format } from 'date-fns';

const ViewOrderModal = ({ order, onClose }) => {
    if (!order) return null;

    const statusColors = {
        completed: 'bg-green-100 text-green-800 border-green-300',
        preparing: 'bg-blue-100 text-blue-800 border-blue-300',
        cancelled: 'bg-red-100 text-red-800 border-red-300',
        pending: 'bg-yellow-100 text-yellow-800 border-yellow-300'
    };

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm animate-in fade-in duration-200">
            <div className="bg-white rounded-xl shadow-xl max-w-lg w-full p-6 relative border border-gray-200">
                <button
                    onClick={onClose}
                    className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                </button>

                {/* Order ID and Time */}
                <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-bold text-gray-800">Order Details</h2>
                    <div className={`px-3 py-1 rounded-full text-sm font-medium ${statusColors[order.status] || 'bg-gray-100 text-gray-800'}`}>
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                    </div>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="col-span-2 sm:col-span-1">
                        <p className="text-sm text-gray-500">Order ID</p>
                        <p className="font-medium text-gray-900">#{order._id}</p>
                    </div>
                    <div className="col-span-2 sm:col-span-1">
                        <p className="text-sm text-gray-500">Order Date</p>
                        <p className="font-medium text-gray-900">{format(new Date(order.createdAt), 'dd MM yyyy')}</p>
                    </div>
                    <div className="col-span-2 sm:col-span-1">
                        <p className="text-sm text-gray-500">Order Time</p>
                        <p className="font-medium text-gray-900">{format(new Date(order.createdAt), 'hh:mm a')}</p>
                    </div>
                    <div className="col-span-2 sm:col-span-1">
                        <p className="text-sm text-gray-500">Table Number</p>
                        <p className="font-medium text-gray-900">{order.tableNumber}</p>
                    </div>
                    <div className="col-span-2 sm:col-span-1">
                        <p className="text-sm text-gray-500">Customer Name</p>
                        <p className="font-medium text-gray-900">{order.name || 'Not provided'}</p>
                    </div>
                    <div className="col-span-2 sm:col-span-1">
                        <p className="text-sm text-gray-500">Total Amount</p>
                        <p className="font-medium text-gray-900">₹{order.total?.toFixed(2) || '0.00'}</p>
                    </div>
                </div>

                <div className="mb-4">
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">Order Items</h3>
                    <div className="space-y-3 max-h-64 overflow-y-auto pr-2 rounded-lg">
                        {order.items?.map((item, idx) => (
                            <div key={idx} className="flex justify-between items-center bg-gray-50 p-3 rounded-lg border border-gray-100">
                                <div className="flex items-center gap-2">
                                    <span className="text-green-500 text-lg">🍽️</span>
                                    <span className="font-medium text-gray-800">{item.itemId?.title || 'Unknown Item'}</span>
                                </div>
                                <span className="px-2.5 py-1 bg-gray-200 rounded-full text-sm font-medium text-gray-700">
                                    × {item.quantity}
                                </span>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ViewOrderModal;