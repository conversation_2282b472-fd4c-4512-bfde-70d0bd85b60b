const Joi = require('joi');

const validateItem = (data) => {
  const itemvalidateschema = Joi.object({
    categoryId: Joi.string().optional().label('Category ID'),
    subcategoryId: Joi.string().optional().label('Subcategory ID'),
    title: Joi.string().required().label('Title'),
    detail: Joi.string().optional().allow('').label('Detail'),
    price: Joi.number().required().min(0).label('Price'),
    type: Joi.string().valid('veg', 'non-veg').required().label('Type'),
    image: Joi.string().optional().uri().label('Image URL'),
    menuButton: Joi.boolean().valid(true, false).optional().default(true).label('Menu Button'),
    extras: Joi.array()
      .items(
        Joi.object({
          title: Joi.string().required().label('Extra Title'),
          price: Joi.number().required().min(0).label('Extra Price'),
        })
      )
      .optional()
      .label('Extras'),
  })
    .xor('categoryId', 'subcategoryId') // Ensure either categoryId or subcategoryId is provided
    .messages({
      'object.xor': 'An item must belong to either a category or a subcategory, not both.',
    });

  return itemvalidateschema.validate(data); // Corrected here
};

module.exports = { validateItem };
