import { useEffect, useState, useRef } from "react";
import { RiCloseLine } from "react-icons/ri";
const TableCard = ({ table, onViewOrder }) => {
    const [showDetails, setShowDetails] = useState(false);
    const detailsRef = useRef(null);
    const cardRef = useRef(null);
    const [popupPosition, setPopupPosition] = useState('bottom');

    useEffect(() => {
        if (cardRef.current && showDetails) {
            const rect = cardRef.current.getBoundingClientRect();
            const spaceAbove = rect.top;
            const spaceBelow = window.innerHeight - rect.bottom;
            setPopupPosition(spaceAbove > spaceBelow ? 'top' : 'bottom');
        }
    }, [showDetails]);
    return (
        <div
            ref={cardRef}
            className="relative"
            onMouseEnter={() => setShowDetails(true)}
            onMouseLeave={() => setShowDetails(false)}
            onClick={() => table.status === 'occupied' && setShowDetails(!showDetails)}
        >
            <div
                className={`cursor-pointer rounded-lg p-3 ${table.status === 'available'
                    ? 'bg-green-500/10 hover:bg-green-500/20'
                    : 'bg-red-500/10 hover:bg-red-500/20'
                    }`}
            >
                <div className="text-center">
                    <div className="text-sm font-medium text-gray-300">Table</div>
                    <div
                        className={`text-xl font-bold ${table.status === 'available' ? 'text-green-500' : 'text-red-500'
                            }`}
                    >
                        {table.tableNumber}
                    </div>
                </div>
            </div>

            {table.status === 'occupied' && showDetails && (
                <div
                    ref={detailsRef}
                    className={`
                        fixed sm:absolute 
                        inset-x-0 sm:inset-auto 
                        ${popupPosition === 'top' ? 'sm:bottom-full sm:mb-2' : 'sm:top-full sm:mt-2'}
                        m-4 sm:m-0
                        sm:left-1/2 sm:-translate-x-1/2 
                        w-auto sm:w-48 
                        bg-gray-900 
                        text-white 
                        text-sm 
                        rounded-lg 
                        p-3
                        z-[60]
                        shadow-xl
                    `}
                >
                    <div className="text-xs space-y-2">
                        <div className="flex justify-between items-center">
                            <p className="font-medium">Order Details</p>
                            <button
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setShowDetails(false);
                                }}
                                className="sm:hidden text-gray-400 hover:text-white"
                            >
                                <RiCloseLine className="text-lg" />
                            </button>
                        </div>
                        <div className="space-y-1 pt-1">
                            <p className="flex justify-between">
                                <span className="text-gray-400">Order ID:</span>
                                <span>{table.currentOrder?._id?.slice(-6)}</span>
                            </p>
                            <p className="flex justify-between">
                                <span className="text-gray-400">Start Time:</span>
                                <span>{new Date(table.updatedAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
                            </p>
                            <p className="flex justify-between">
                                <span className="text-gray-400">Items:</span>
                                <span>{table.currentOrder?.items?.length || 0}</span>
                            </p>
                        </div>
                        <button
                            onClick={(e) => {
                                e.stopPropagation();
                                onViewOrder(table.currentOrder?._id);
                            }}
                            className="mt-2 w-full text-center bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 px-2 py-1.5 rounded"
                        >
                            View Order
                        </button>
                    </div>

                    <div
                        className={`hidden sm:block absolute 
                            ${popupPosition === 'top'
                                ? 'bottom-[-8px] border-t-[8px] border-t-gray-900'
                                : 'top-[-8px] border-b-[8px] border-b-gray-900'
                            } 
                            left-1/2 -translate-x-1/2 
                            w-0 h-0 
                            border-l-[8px] border-l-transparent 
                            border-r-[8px] border-r-transparent`
                        }
                    />
                </div>
            )}
        </div>
    );
};

export default TableCard;