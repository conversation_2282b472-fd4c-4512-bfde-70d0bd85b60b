import { Route, Routes, Outlet } from 'react-router-dom'
import MainLayout from './components/Layout/layout'
import { Login, SignUp, Dashboard, Order, EditProfile, ORCode, Restauarent, RMenu, Umenu, Waiters, WaiterDashboard, WaiterMenu, WaiterProfile, ResetPassword, ForgotPassword } from './pages'
import { ProtectedRoute, CheckRole } from './components/RouteProtector';
import { Toaster } from 'react-hot-toast';
import Notifications from './pages/Notifications/Notifications';
import ForgotPasswordEmail from './pages/Email_Temp/Forgottemp';
import Normalmenu from './pages/UMenu/Normalmenu';


function App() {

  return (
    <>
      <Toaster position="top-center" />
      <Routes>
        <Route path="/" element={<ProtectedRoute>
          <CheckRole allowedRoles={['admin']}>
            <MainLayout />
          </CheckRole>
        </ProtectedRoute>}>
          <Route index element={<Dashboard />} />
          <Route path="restaurant" element={<Restauarent />} />
          <Route path="order" element={<Order />} />
          <Route path="edit-profile" element={<EditProfile />} />
          <Route path="qr-code" element={<ORCode />} />
          <Route path="menu" element={<RMenu />} />
          <Route path="notifications" element={<Notifications />} />
          <Route path="waiters" element={<Waiters />} />
        </Route>

        <Route path='/staff' element={<ProtectedRoute><CheckRole allowedRoles={['waiter', 'chef']}><Outlet /></CheckRole></ProtectedRoute>}>
          <Route path='dashboard' element={<WaiterDashboard />} />
          <Route path='menu' element={<WaiterMenu />} />
          <Route path='profile' element={<WaiterProfile />} />
        </Route>

        <Route path="/login" element={<Login />} />
        <Route path="/signup" element={<SignUp />} />
        <Route path="/restaurant/:slug" element={<Umenu />} />
        <Route path="/normalmenu/:slug" element={<Normalmenu />} />

        <Route path="/temp" element={<ForgotPasswordEmail />} />

        <Route path="/reset-password" element={<ResetPassword />} />
        <Route path="/forgot-password" element={<ForgotPassword />} />

      </Routes>

    </>
  )
}

export default App
