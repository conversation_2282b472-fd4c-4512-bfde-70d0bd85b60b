// Load environment variables from .env file
const dotenv = require('dotenv');
dotenv.config();
const createError = require('http-errors');
const express = require('express');
const path = require('path');
const cookieParser = require('cookie-parser');
const logger = require('morgan');
const errorhandler = require("./middleware/errorhandler");
const cors = require("cors");

const initializeRoute = require('./routes/index.route'); // to route file
const connectDB = require("./config/db.config"); // connect to database file

const app = express();

app.use(logger('dev'));
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));
app.use(cors({
  origin: true,
  credentials: true,
}));

//connect to database 
connectDB();


//routes
app.use('/', initializeRoute);

// // catch 404 and forward to error handler
app.use(function (req, res, next) {
  next(createError(404));
});

// Use the error handler middleware at the end of all routes
app.use(errorhandler);

module.exports = app;
