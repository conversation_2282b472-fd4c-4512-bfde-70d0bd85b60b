import { useState, useContext, useEffect } from "react";
import { RestaurantDataContext } from "../../context/RestaurantContext";
import { toast } from "react-hot-toast";
import { Loader2 } from "lucide-react";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "../../components/ui/select";
import InputField from "../../components/InputFieldValidation/InputField";
import {
  postSignupData,
  updateUser,
  getAllRestaurantStaff,
  deleteStaffAccount,
} from "../../api";
import { format } from "date-fns";
const Waiters = () => {
  const { restaurantData } = useContext(RestaurantDataContext);
  const [waiters, setWaiters] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingWaiter, setEditingWaiter] = useState(null);

  // Updated formData with additional fields
  const [formData, setFormData] = useState({
    name: "",
    username: "",
    phone: "",
    email: "",
    password: "",
    role: "",
    restaurant_detail: {
      id: null,
      slug: null,
    },
    shift: "",
    working_hours: ""
  });

  useEffect(() => {
    async function getRestaurantStaff(id) {
      try {
        const response = await getAllRestaurantStaff(id);
        if (response.data.success) {
          setWaiters(response.data.data);
        }
      } catch (error) {
        toast.error(
          error.response?.data?.message ||
          "An error occurred. Please try again."
        );
      }
    }
    if (!restaurantData) return;
    getRestaurantStaff(restaurantData._id);
  }, [restaurantData]);
  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name) {
      newErrors.name = "Name name is required";
    } else if (formData.name.length < 2) {
      newErrors.name = "Name must be at least 2 characters long";
    }

    if (!formData.username) {
      newErrors.username = "Username is required";
    } else if (formData.username.length < 3) {
      newErrors.username = "Username must be at least 3 characters";
    }

    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Invalid email format";
    }

    if (!formData.phone) {
      newErrors.phone = "Phone number is required";
    } else if (formData.phone.length < 10) {
      newErrors.phone = "Phone number must be at least 10 digits";
    } else if (!/^\d{10}$/.test(formData.phone)) {
      newErrors.phone = "Invalid phone number";
    }
    if (!editingWaiter) {
      if (!formData.password) {
        newErrors.password = "Password is required";
      } else if (formData.password.length < 6) {
        newErrors.password = "Password must be at least 6 characters";
      } else if (
        !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{6,}$/.test(
          formData.password
        )
      ) {
        newErrors.password =
          "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character";
      }
    }
    if (!formData.role) {
      newErrors.role = "Role is required";
    }
    if (!formData.shift) {
      newErrors.shift = "Shift is required"
    }
    if (!formData.working_hours) {
      newErrors.working_hours = "Staff Working Hour is required"
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const handleFormDataChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;
    setLoading(true);

    if (editingWaiter) {
      // Update an existing waiter
      try {
        const updatedData = {};
        for (const key in formData) {
          if (formData[key] !== editingWaiter[key]) {
            updatedData[key] = formData[key];
          }
        }

        const response = await updateUser(editingWaiter.userID, updatedData);

        if (response.data.success) {
          const updatedWaiter = {
            ...editingWaiter,
            ...updatedData,
          };
          setWaiters((prev) =>
            prev.map((waiter) =>
              waiter.userID === editingWaiter.userID ? updatedWaiter : waiter
            )
          );
          toast.success("User updated successfully");
          setFormData({
            name: "",
            username: "",
            phone: "",
            email: "",
            password: "",
            role: "",
            restaurant_detail: {
              id: null,
              slug: null,
            },
            shift: "",
            working_hours: ""
          });
          setIsModalOpen(false);
        }
      } catch (error) {
        toast.error(
          error.response?.data?.message ||
          "An error occurred. Please try again."
        );
      } finally {
        setLoading(false);
      }
    } else {
      try {
        const restaurantId = restaurantData._id;
        const restaurantSlug = restaurantData.slug;
        if (!restaurantId || !restaurantSlug) {
          return;
        }
        (formData.restaurant_detail.id = restaurantId),
          (formData.restaurant_detail.slug = restaurantSlug),
          console.log(formData);
        const response = await postSignupData(formData);
        console.log(response);
        if (response.data.success) {
          setWaiters((prev) => [
            ...prev,
            {
              userID: response.data.user._id,
              name: response.data.user.name,
              username: response.data.user.username,
              phone: response.data.user.phone,
              email: response.data.user.email,
              role: response.data.user.role,
              createdAt: response.data.user.createdAt,
              updatedAt: response.data.user.updatedAt,
              restaurant_detail: {
                id: response.data.user.staffDetails.restaurant_detail.id,
                slug: response.data.user.staffDetails.restaurant_detail.slug,
              },
              status: response.data.user.staffDetails.status,
              shift: response.data.user.staffDetails.shift,
              working_hours: response.data.user.staffDetails.working_hours
            },
          ]);
          toast.success("Waiter added successfully");
          setFormData({
            name: "",
            username: "",
            phone: "",
            email: "",
            password: "",
            role: "",
            restaurant_detail: {
              id: null,
              slug: null,
            },
            shift: "",
            working_hours: ""
          });
          setIsModalOpen(false);
        }
      } catch (error) {
        console.log(error.response);
        toast.error(
          error.response?.data?.message ||
          "An error occurred. Please try again."
        );
      } finally {
        setLoading(false);
      }
    }
  };
  useEffect(() => {
    console.log('waiters', waiters)
  }, [waiters])


  const handleEdit = (waiter) => {
    console.log(waiter)
    setEditingWaiter(waiter);
    setFormData({
      name: waiter.name,
      username: waiter.username,
      phone: waiter.phone,
      email: waiter.email,
      password: waiter.password,
      role: waiter.role,
      restaurant_detail: {
        id: waiter.restaurant_detail.id,
        slug: waiter.restaurant_detail.slug,
      },
      shift: waiter.shift,
      working_hours: waiter.working_hours
    });

    setIsModalOpen(true);
  };

  const handleRemove = async (waiterId) => {
    try {

      const response = await deleteStaffAccount(waiterId);
      if (response.data.success) {
        setWaiters((prev) =>
          prev.filter((waiter) => waiter.userID !== waiterId)
        );
        toast.success(response.data.message);
      }
    } catch (error) {
      console.log(error);
      toast.error(
        error.response?.data?.message || "An error occurred. Please try again."
      );
    }
  };

  const closeModal = () => {
    setEditingWaiter(null);
    setIsModalOpen(false);
    setFormData({
      name: "",
      username: "",
      phone: "",
      email: "",
      password: "",
      role: "",
      restaurant_detail: {
        id: null,
        slug: null,
      },
      shift: "",
      working_hours: ""
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header Section */}
        <div className="bg-white rounded-3xl shadow-xl p-6 md:p-8 border border-gray-200">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-4">
              <div className="h-12 w-12 bg-[#EFA052]/10 rounded-xl flex items-center justify-center">
                <svg
                  className="w-6 h-6 text-[#EFA052]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                  />
                </svg>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">
                  Waiters Management
                </h1>
                <p className="text-gray-500">Manage your restaurant&#39;s staff</p>
              </div>
            </div>
            <button
              onClick={() => setIsModalOpen(true)}
              className="px-4 py-2 bg-gradient-to-r from-[#EFA052] to-[#D88A3B] text-white rounded-xl font-semibold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-[#EFA052]/25 focus:outline-none focus:ring-2 focus:ring-[#EFA052] focus:ring-offset-2 flex items-center gap-2"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              {editingWaiter ? "Edit Waiter" : "Add Waiter"}
            </button>
          </div>
        </div>

        {/* Waiters List */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {waiters.map((waiter) => (
              <div
                key={waiter.userID}
                className="bg-white rounded-2xl shadow-lg border border-gray-200 p-6"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-4">
                    <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center">
                      <span className="text-xl font-semibold text-gray-600">
                        {waiter.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-800">
                        {waiter.name}
                      </h3>
                      <p className="text-sm text-gray-500">{waiter.email}</p>
                    </div>
                  </div>
                  <span
                    className={`px-3 py-1 rounded-full text-xs font-medium ${waiter.status === "active"
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                      }`}
                  >
                    {waiter.status}
                  </span>
                </div>
                <div className="mt-4 space-y-2">
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Phone:</span> {waiter.phone}
                  </p>

                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Role : </span> {waiter.role}
                  </p>

                  {waiter.createdAt && (
                    <p className="text-xs text-gray-400">
                      <span className="font-medium">Added on :</span>{" "}
                      {format(new Date(waiter.createdAt), "dd/MM/yyyy")}
                    </p>
                  )}
                </div>
                <div className="mt-6 flex gap-2">
                  <button
                    onClick={() => handleEdit(waiter)}
                    className="flex-1 px-4 py-2 text-[#EFA052] border border-[#EFA052] rounded-xl hover:bg-[#EFA052]/5 transition-colors"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleRemove(waiter.userID)}
                    className="flex-1 px-4 py-2 text-red-500 border border-red-500 rounded-xl hover:bg-red-50 transition-colors"
                  >
                    Remove
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Add/Edit Waiter Modal */}
      {isModalOpen && (
        <div className="fixed inset-0  bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-3xl h-screen shadow-xl max-w-2xl w-full p-6 md:p-8">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-semibold text-gray-800">
                {editingWaiter ? "Edit Waiter Details" : "Add New Waiter"}
              </h2>
              <button
                onClick={closeModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Name & Username */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                <InputField
                  label={"Name"}
                  name="name"
                  value={formData.name}
                  onChange={handleFormDataChange}
                  placeholder="Enter name"
                  error={errors.name}
                />
                <InputField
                  label={"Username"}
                  name="username"
                  value={formData.username}
                  onChange={handleFormDataChange}
                  placeholder="Enter username"
                  error={errors.username}
                />
              </div>
              {/* Phone & Email */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                <InputField
                  label={"Email"}
                  name="email"
                  value={formData.email}
                  onChange={handleFormDataChange}
                  placeholder="Enter email address"
                  error={errors.email}
                />
                <InputField
                  label={"Phone"}
                  name="phone"
                  value={formData.phone}
                  onChange={handleFormDataChange}
                  placeholder="Enter phone number"
                  error={errors.phone}
                  maxLength={10}
                />
              </div>
              {/* Role &  Password */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Role
                  </label>
                  <Select
                    name="role"
                    onValueChange={(value) => {
                      setFormData({ ...formData, role: value });
                      setErrors({ ...errors, role: "" });
                    }}
                    value={formData.role}
                  >
                    <SelectTrigger
                      className={`w-full ${errors.role
                        ? "border-red-500 focus:border-red-500 focus:ring-red-200"
                        : "border-gray-200"
                        }`}
                    >
                      <SelectValue placeholder="Select option" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="waiter">Waiter</SelectItem>
                      <SelectItem value="chef">Chef</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.role && (
                    <p className="text-red-500 text-sm mt-1">{errors.role}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <InputField
                    label={"Password"}
                    name="password"
                    value={formData.password}
                    onChange={handleFormDataChange}
                    placeholder="Enter password"
                    error={errors.password}
                  />
                </div>
              </div>
              {/* shift & wotking hour */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Shift
                  </label>
                  <Select
                    name="shift"
                    onValueChange={(value) => {
                      setFormData({ ...formData, shift: value });
                      setErrors({ ...errors, shift: "" });
                    }}
                    value={formData.shift}
                  >
                    <SelectTrigger
                      className={`w-full ${errors.shift
                        ? "border-red-500 focus:border-red-500 focus:ring-red-200"
                        : "border-gray-200"
                        }`}
                    >
                      <SelectValue placeholder="Select option" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="day">Day</SelectItem>
                      <SelectItem value="night">Night</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.role && (
                    <p className="text-red-500 text-sm mt-1">{errors.shift}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <InputField
                    type="number"
                    label={"Working Hours"}
                    name="working_hours"
                    value={formData.working_hours}
                    onChange={handleFormDataChange}
                    placeholder="Enter total working hours of staff"
                    error={errors.working_hours}
                    min={0}
                  />
                </div>
              </div>

              <button
                type="submit"
                className="w-full py-3 bg-gradient-to-r from-[#EFA052] to-[#D88A3B] text-white rounded-xl font-semibold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-[#EFA052]/25 focus:outline-none focus:ring-2 focus:ring-[#EFA052] focus:ring-offset-2"
              >
                {editingWaiter ? "Update Waiter" : "Add Waiter"}
              </button>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Waiters;
