const Joi = require('joi');

// Restaurant validation schema using Jo<PERSON>
const restaurantValidationSchema = Joi.object({
  name: Joi.string()
    .min(3)
    .max(100) // Changed to match model maxlength
    .required()
    .trim()
    .messages({
      'string.base': '"name" should be a type of "string"',
      'string.empty': '"name" cannot be empty', 
      'string.min': '"name" should have a minimum length of 3 characters',
      'string.max': '"name" should have a maximum length of 100 characters',
      'any.required': '"name" is a required field',
    }),

  slug: Joi.string()
    .min(3)
    .max(100) // Changed to match model maxlength
    .required()
    .trim()
    .messages({
      'string.base': '"slug" should be a type of "string"',
      'string.empty': '"slug" cannot be empty',
      'string.pattern.base': '"slug" should only contain alphanumeric characters and hyphens',
      'string.min': '"slug" should have a minimum length of 3 characters', 
      'string.max': '"slug" should have a maximum length of 100 characters',
      'any.required': '"slug" is a required field',
    }),

  description: Joi.string()
    .max(500)
    .optional()
    .trim()
    .messages({
      'string.base': '"description" should be a type of "string"',
      'string.max': '"description" should have a maximum length of 500 characters',
    }),

  location: Joi.string()
    .min(5)
    .max(200) // Changed to match model maxlength
    .required()
    .trim()
    .messages({
      'string.base': '"location" should be a type of "string"',
      'string.empty': '"location" cannot be empty',
      'string.min': '"location" should have a minimum length of 5 characters',
      'string.max': '"location" should have a maximum length of 200 characters',
      'any.required': '"location" is a required field',
    }),

  subTitle: Joi.string()
    .max(150)
    .optional()
    .trim()
    .messages({
      'string.base': '"subTitle" should be a type of "string"',
      'string.max': '"subTitle" should have a maximum length of 150 characters',
    }),

  timing: Joi.string()
    .min(5)
    .max(100) // Changed to match model maxlength
    .required()
    .trim()
    .messages({
      'string.base': '"timing" should be a type of "string"',
      'string.empty': '"timing" cannot be empty',
      'string.min': '"timing" should have a minimum length of 5 characters',
      'string.max': '"timing" should have a maximum length of 100 characters',
      'any.required': '"timing" is a required field',
    }),

  image: Joi.string()
    .uri()
    .trim()
    .messages({
      'string.uri': '"image" should be a valid URI',
      'any.required': '"image" is a required field',
    }),

  coverImage: Joi.string()
    .uri()
    .trim()
    .messages({
      'string.uri': '"coverImage" should be a valid URI',
      'any.required': '"coverImage" is a required field',
    }),

  allowTAX: Joi.string() // Changed to match model field name and type
    .valid('yes', 'no')
    .default('no')
    .messages({
      'string.base': '"allowTAX" should be a string',
      'any.only': '"allowTAX" must be either "yes" or "no"'
    }),

  taxPercentage: Joi.number()
    .min(0)
    .max(100)
    .when('allowTAX', { // Added conditional requirement based on allowTAX
      is: 'yes',
      then: Joi.required(),
      otherwise: Joi.optional()
    })
    .messages({
      'number.base': '"taxPercentage" should be a number',
      'number.min': '"taxPercentage" cannot be less than 0',
      'number.max': '"taxPercentage" cannot be more than 100',
      'any.required': '"taxPercentage" is required when allowTAX is "yes"'
    }),

  gstID: Joi.string() // Changed to match model field name
    .pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/) // Changed to match model regex pattern
    .optional()
    .trim()
    .messages({
      'string.pattern.base': '"gstID" should be a 15-digit number',
    }),
    
  allowOnTableOrder: Joi.string()
    .valid('yes', 'no')
    .default('no')
    .messages({
      'string.base': '"allowOnTableOrder" should be a string',
      'any.only': '"allowOnTableOrder" must be either "yes" or "no"'
    }),

  numberOfTables: Joi.number()
    .min(1)
    .max(100)
    .optional()
    .messages({
      'number.base': '"numberOfTables" should be a number',
      'number.min': '"numberOfTables" cannot be less than 1',
      'number.max': '"numberOfTables" cannot be more than 100',
    }),
});

module.exports = {
  restaurantValidationSchema,
};
