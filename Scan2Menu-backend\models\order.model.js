const mongoose = require('mongoose');
const Item = require('../models/item.model');
const Restaurant = require('../models/restaurant.model');

const orderSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100,
    },
    restaurantId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Restaurant',
      required: true
    },
    tableNumber: {
      type: Number,
      required: true,
      min: 1,
    },
    phoneNumber: {
      type: String,
      required: true,
      match: /^[0-9]{10}$/, // Ensures only 10-digit phone numbers are stored
    },
    subtotal: {
      type: Number,
      required: true,
      min: 0,
    },
    gst: {
      type: Number,
      required: true,
      min: 0,
    },
    total: {
      type: Number,
      required: true,
      min: 0,
    },
    items: [
      {
        itemId: { type: mongoose.Schema.Types.ObjectId, ref: 'Item', required: true },
        quantity: { type: Number, required: true, min: 1 },
        selectedExtras: [
          {
            _id: { type: mongoose.Schema.Types.ObjectId, ref: 'Item.extras' },
            title: String,
            price: Number,
          }
        ],
      },
    ],
    status: {
      type: String,
      enum: ['pending', 'preparing', 'served', 'completed', 'cancelled'],
      default: 'pending',
    },
    orderby: {
      type: String,
      required: true,
      default: 'user',
    },
    paymentStatus: {
      type: String,
      enum: ['pending', 'completed', 'failed'],
      default: 'pending'
    },
    razorpayOrderId: {
      type: String,
    },
    razorpayPaymentId: {
      type: String,
    }
  },
  { timestamps: true }
);

const Order = mongoose.model('Order', orderSchema);
module.exports = Order;