import React, { useState, useEffect, useContext } from 'react';
import {
  RiAddLine,
  RiEditLine,
  RiDeleteBinLine,
  RiMenuLine,
  RiImageAddLine,
  RiLayoutGridLine,
  RiCloseLine
} from 'react-icons/ri';
import { GripVertical } from 'lucide-react';
import { Button } from '../../components/ui/button';
import DialogForm from '../../components/DialogForm/DialogForm';
import AddItemModal from '../../components/Modal/AddItemModal';
import AddExtraModal from "../../components/Modal/AddExtraModal"
import { addCategory, getAllCategories, updateCategory, deleteCategory, addSubcategory, updateSubcategory, deleteSubcategory, addItem, updateItem, deleteItem, addExtra, removeExtra, reorderCategories, reorderItems } from '../../api';
import { RestaurantDataContext } from '../../context/RestaurantContext';
import { toast } from 'react-hot-toast';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

const SortableCategory = ({
  category,
  isDragging,
  toggleCategory,
  expandedCategories,
  expandedSubcategories,
  toggleSubcategory,
  handleAddItem,
  handleOpenDialog,
  handleEditItem,
  handleDeleteItem,
  handleDeleteSubcategory,
  handleAddExtra,
  handleDeleteExtra
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isCategoryDragging,
  } = useSortable({ id: `category-${category._id}` });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isCategoryDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-200 hover:shadow-md ${isDragging ? 'pointer-events-none' : ''}`}
    >
      <div
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center p-4 sm:p-5 cursor-pointer hover:bg-gray-50 transition-colors duration-200 gap-4"
        onClick={() => toggleCategory(category._id)}
      >
        <div className="flex items-center gap-4">
          {/* Drag Handle */}
          <div
            {...attributes}
            {...listeners}
            className="cursor-grab active:cursor-grabbing p-1 hover:bg-gray-200 rounded flex-shrink-0"
            onClick={(e) => e.stopPropagation()}
          >
            <GripVertical className="w-5 h-5 text-gray-400" />
          </div>
          <div className="p-2 bg-gray-100 rounded-lg">
            <RiMenuLine className="text-xl text-gray-600" />
          </div>
          <span className="text-lg font-medium text-gray-800">{category.categoryName}</span>
        </div>
        <div className="flex items-center gap-2 sm:gap-3 w-full sm:w-auto justify-end">
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleAddItem(category._id);
            }}
            className="p-2 sm:p-2.5 bg-[#22C55E] text-white rounded-lg hover:bg-[#1ea952] transition-colors duration-200"
            title="Add Item"
          >
            <RiImageAddLine className="text-lg sm:text-xl" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleOpenDialog('Subcategory', '', category._id);
            }}
            className="p-2.5 bg-[#22C55E] text-white rounded-lg hover:bg-[#1ea952] transition-colors duration-200"
            title="Add Subcategory"
          >
            <RiAddLine className="text-xl" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleOpenDialog('Category', category.categoryName, category._id);
            }}
            className="p-2.5 bg-[#F59E0B] text-white rounded-lg hover:bg-[#d88c07] transition-colors duration-200"
            title="Edit Category"
          >
            <RiEditLine className="text-xl" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteCategory(category._id);
            }}
            className="p-2.5 bg-[#EF4444] text-white rounded-lg hover:bg-[#dc2626] transition-colors duration-200"
            title="Delete Category"
          >
            <RiDeleteBinLine className="text-xl" />
          </button>
        </div>
      </div>

      {expandedCategories[category._id] && (
        <div className="border-t border-gray-100">
          {/* Category Items */}
          {category.items?.length > 0 && (
            <div className="p-4 sm:p-5 space-y-3">
              <SortableContext
                items={category.items.map(item => `item-${item._id}`)}
                strategy={verticalListSortingStrategy}
              >
                {category.items.map((item) => (
                  <SortableMenuItem
                    key={item._id}
                    item={item}
                    categoryId={category._id}
                    handleEditItem={handleEditItem}
                    handleDeleteItem={handleDeleteItem}
                    handleAddExtra={handleAddExtra}
                    handleDeleteExtra={handleDeleteExtra}
                  />
                ))}
              </SortableContext>
            </div>
          )}

          {/* Subcategories */}
          {category.subcategories?.map((subcategory) => (
            <div key={subcategory._id} className="border-t border-gray-100">
              <div
                className="flex flex-col sm:flex-row justify-between items-start sm:items-center p-4 sm:p-5 pl-8 sm:pl-10 cursor-pointer hover:bg-gray-50 transition-colors duration-200 gap-4"
                onClick={() => toggleSubcategory(subcategory._id)}
              >
                <div className="flex items-center gap-4">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    <RiMenuLine className="text-xl text-gray-600" />
                  </div>
                  <span className="text-lg font-medium text-gray-800">{subcategory.subcategoryName}</span>
                </div>
                <div className="flex items-center gap-2 sm:gap-3 w-full sm:w-auto justify-end">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddItem("", subcategory._id);
                    }}
                    className="p-2 sm:p-2.5 bg-[#22C55E] text-white rounded-lg hover:bg-[#1ea952] transition-colors duration-200"
                    title="Add Item"
                  >
                    <RiImageAddLine className="text-lg sm:text-xl" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOpenDialog('Subcategory', subcategory.subcategoryName, category._id, subcategory._id);
                    }}
                    className="p-2.5 bg-[#F59E0B] text-white rounded-lg hover:bg-[#d88c07] transition-colors duration-200"
                    title="Edit Subcategory"
                  >
                    <RiEditLine className="text-xl" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteSubcategory(subcategory._id);
                    }}
                    className="p-2.5 bg-[#EF4444] text-white rounded-lg hover:bg-[#dc2626] transition-colors duration-200"
                    title="Delete Subcategory"
                  >
                    <RiDeleteBinLine className="text-xl" />
                  </button>
                </div>
              </div>

              {/* Subcategory Items */}
              {expandedSubcategories[subcategory._id] && subcategory.items && (
                <div className="p-4 sm:p-5 pl-12 sm:pl-16 space-y-3">
                  <SortableContext
                    items={subcategory.items.map(item => `item-${item._id}`)}
                    strategy={verticalListSortingStrategy}
                  >
                    {subcategory.items.map((item) => (
                      <SortableMenuItem
                        key={item._id}
                        item={item}
                        categoryId={category._id}
                        subcategoryId={subcategory._id}
                        handleEditItem={handleEditItem}
                        handleDeleteItem={handleDeleteItem}
                        handleAddExtra={handleAddExtra}
                        handleDeleteExtra={handleDeleteExtra}
                      />
                    ))}
                  </SortableContext>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const SortableMenuItem = ({ item, categoryId, subcategoryId, handleEditItem, handleDeleteItem, handleAddExtra, handleDeleteExtra }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isItemDragging,
  } = useSortable({ id: `item-${item._id}` });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isItemDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex flex-col sm:flex-row justify-between items-start sm:items-center p-3 sm:p-4 bg-white rounded-xl border border-gray-100 hover:bg-gray-50 transition-colors duration-200 gap-4"
    >
      <div className="flex items-center gap-4 w-full sm:w-auto">
        {/* Drag Handle */}
        <div
          {...attributes}
          {...listeners}
          className="cursor-grab active:cursor-grabbing p-1 hover:bg-gray-200 rounded flex-shrink-0"
        >
          <GripVertical className="w-4 h-4 text-gray-400" />
        </div>
        <img src={item.subimage} className="w-14 h-14 sm:w-16 sm:h-16 rounded-xl object-cover" alt={item.title} />
        <div>
          <span className="text-base sm:text-lg font-medium text-gray-800">{item.title}</span>
          <div className="text-sm text-gray-600 mt-1">
            Price: ${item.price} | Type: {item.type}
          </div>
          {item.extras && item.extras.length > 0 && (
            <div className="mt-2">
              <span className="text-sm font-medium text-gray-700">Extras:</span>
              <div className="flex flex-wrap gap-2 mt-1">
                {item.extras.map((extra) => (
                  <div
                    key={extra._id}
                    className="flex items-center gap-2 bg-gray-100 px-2 py-1 rounded-lg text-sm"
                  >
                    <span>{extra.title} - ${extra.price}</span>
                    <button
                      onClick={() => handleDeleteExtra(item._id, extra._id)}
                      className="text-red-500 hover:text-red-700 transition-colors duration-200"
                      title="Remove Extra"
                    >
                      <RiCloseLine className="text-lg" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
      <div className="flex gap-2 sm:gap-3 w-full sm:w-auto justify-end">
        <button
          onClick={() => handleAddExtra(item._id)}
          className="p-2.5 bg-[#22C55E] text-white rounded-lg hover:bg-[#1ea952] transition-colors duration-200"
          title="Add Extra"
        >
          <RiAddLine className="text-xl" />
        </button>
        <button
          onClick={() => handleEditItem(item, categoryId, subcategoryId || "")}
          className="p-2.5 bg-[#F59E0B] text-white rounded-lg hover:bg-[#d88c07] transition-colors duration-200"
          title="Edit Item"
        >
          <RiEditLine className="text-xl" />
        </button>
        <button
          onClick={() => handleDeleteItem(item._id)}
          className="p-2.5 bg-[#EF4444] text-white rounded-lg hover:bg-[#dc2626] transition-colors duration-200"
          title="Delete Item"
        >
          <RiDeleteBinLine className="text-xl" />
        </button>
      </div>
    </div>
  );
};

const RMenu = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isItemModalOpen, setIsItemModalOpen] = useState(false);
  const [isExtraModalOpen, setIsExtraModalOpen] = useState(false);
  const [DialogType, setDialogType] = useState("");
  const [initialFormValue, setInitialFormValue] = useState("");
  const [currentCategoryId, setCurrentCategoryId] = useState(null);
  const [currentSubcategoryId, setCurrentSubcategoryId] = useState(null);
  const [editingItem, setEditingItem] = useState(null);
  const [currentItemId, setCurrentItemId] = useState(null);
  const { restaurantData } = useContext(RestaurantDataContext);
  const [categories, setCategories] = useState([]);
  const [expandedCategories, setExpandedCategories] = useState({});
  const [expandedSubcategories, setExpandedSubcategories] = useState({});
  const [loading, setLoading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleOpenDialog = (type, initialValue = "", categoryId = null, subcategoryId = null) => {
    if (!restaurantData || !restaurantData._id) {
      toast.error("Please create a restaurant first.");
      return;
    }
    setDialogType(type);
    setInitialFormValue(initialValue);
    setCurrentCategoryId(categoryId);
    setCurrentSubcategoryId(subcategoryId);
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setDialogType("");
    setInitialFormValue("");
    setCurrentCategoryId(null);
    setCurrentSubcategoryId(null);
  };

  const handleDialogFormDataSubmit = async (formValue) => { //category or subcategory
    if (DialogType === 'Category') {
      try {
        if (initialFormValue) {
          // Update category
          let response = await updateCategory(currentCategoryId, { categoryName: formValue });
          if (response.data.success) {

            setCategories(categories.map(category => category._id === currentCategoryId ?
              { ...category, categoryName: response.data.category.categoryName }
              : category));
            toast.success(response.data.message);
          }
        } else {
          // Add category
          const response = await addCategory({ categoryName: formValue, restaurantId: restaurantData._id });
          if (response.data.success) {
            setCategories([...categories, response.data.category]);
            toast.success(response.data.message);
          }

        }
      } catch (error) {
        toast.error(error.response.data.message || error.message);
      }
    } else if (DialogType === 'Subcategory') {
      try {
        if (initialFormValue) {
          // Update subcategory
          const response = await updateSubcategory(currentSubcategoryId, { subcategoryName: formValue });
          if (response.data.success) {
            setCategories(categories.map(category => ({
              ...category,
              subcategories: category.subcategories.map(subcategory => subcategory._id === currentSubcategoryId ?
                { ...subcategory, subcategoryName: response.data.subcategory.subcategoryName }
                : subcategory)
            })));
            toast.success(response.data.message);
          }
        } else {
          // Add subcategory
          const response = await addSubcategory({ subcategoryName: formValue, categoryId: currentCategoryId });
          if (response.data.success) {
            setCategories(categories.map(category => category._id === currentCategoryId ?
              { ...category, subcategories: [...category.subcategories, response.data.subcategory] }
              : category));
            toast.success(response.data.message);
          }
        }
      } catch (error) {
        toast.error(error.response.data.message || error.message);
      }
    }
  };

  const handleDeleteCategory = async (categoryId) => {
    try {
      let response = await deleteCategory(categoryId);
      if (response.data.success) {
        setCategories(categories.filter((category) => category._id !== categoryId));
        toast.success(response.data.message);
      }
    } catch (error) {
      toast.error(error.response.data.message || error.message);
    }
  };

  const handleDeleteSubcategory = async (subcategoryId) => {
    try {
      if (!subcategoryId) return;
      let response = await deleteSubcategory(subcategoryId);
      if (response.data.success) {
        setCategories(categories.map(category => ({
          ...category,
          subcategories: category.subcategories.filter(subcategory => subcategory._id !== subcategoryId)
        })));
        toast.success(response.data.message);
      }
    } catch (error) {
      toast.error(error.response.data.message || error.message);
    }
  };

  const handleAddItem = (categoryId = null, subcategoryId = null) => {
    setCurrentCategoryId(categoryId);
    setCurrentSubcategoryId(subcategoryId);
    setEditingItem(null);
    setIsItemModalOpen(true);
  };

  const handleEditItem = (item, categoryId = null, subcategoryId = null) => {
    setEditingItem(item);
    setCurrentCategoryId(categoryId);
    setCurrentSubcategoryId(subcategoryId);
    setIsItemModalOpen(true);
  };

  const handleDeleteItem = async (itemId) => {
    try {
      let response = await deleteItem(itemId);
      if (response.data.success) {
        setCategories(categories.map(category => ({
          ...category,
          subcategories: category.subcategories.map(subcategory => ({
            ...subcategory,
            items: subcategory.items.filter(item => item._id !== itemId)
          })),
          items: category.items.filter(item => item._id !== itemId)
        })));
        toast.success(response.data.message);
      }
    } catch (error) {
      toast.error(error.response.data.message || error.message);
    }
  };

  const handleAddExtra = (itemId) => {
    setCurrentItemId(itemId);
    setIsExtraModalOpen(true);
  };

  const handleSaveExtra = async (extraData) => {
    try {
      setLoading(true);
      const response = await addExtra(currentItemId, extraData);
      if (response.data.success) {
        setCategories(categories.map(category => ({
          ...category,
          subcategories: category.subcategories.map(subcategory => ({
            ...subcategory,
            items: subcategory.items.map(item =>
              item._id === currentItemId ? response.data.item : item
            )
          })),
          items: category.items.map(item =>
            item._id === currentItemId ? response.data.item : item
          )
        })));
        toast.success(response.data.message);
        setIsExtraModalOpen(false);
      }
    } catch (error) {
      toast.error(error.response.data.message || error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteExtra = async (itemId, extraId) => {
    try {
      const response = await removeExtra(itemId, extraId);
      if (response.data.success) {
        setCategories(categories.map(category => ({
          ...category,
          subcategories: category.subcategories.map(subcategory => ({
            ...subcategory,
            items: subcategory.items.map(item =>
              item._id === itemId ? response.data.item : item
            )
          })),
          items: category.items.map(item =>
            item._id === itemId ? response.data.item : item
          )
        })));
        toast.success(response.data.message);
      }
    } catch (error) {
      toast.error(error.response.data.message || error.message);
    }
  };

  const handleSaveItem = async (itemData) => {
    try {
      setLoading(true);
      if (editingItem) {
        // Update item
        let response = await updateItem(editingItem._id, itemData);
        if (response.data.success) {
          setCategories(categories.map(category => ({
            ...category,
            subcategories: category.subcategories.map(subcategory => ({
              ...subcategory,
              items: subcategory.items.map(item => item._id === editingItem._id ? response.data.item : item)
            })),
            items: category.items.map(item => item._id === editingItem._id ? response.data.item : item)
          })));
          toast.success(response.data.message);
        }
      } else {
        // Add item
        let response = await addItem(itemData);
        if (response.data.success) {
          setCategories(categories.map(category => {
            if (currentSubcategoryId) {
              return {
                ...category,
                subcategories: category.subcategories.map(subcategory => subcategory._id === currentSubcategoryId ?
                  { ...subcategory, items: [...subcategory.items, response.data.item] } :
                  subcategory)
              };
            } else if (category._id === currentCategoryId) {
              return { ...category, items: [...category.items, response.data.item] };
            }
            return category;
          }));
          toast.success(response.data.message);
        }
      }
      setIsItemModalOpen(false);
    } catch (error) {
      toast.error(error.response.data.message || error.message);
    } finally {
      setLoading(false);
    }
  };

  const toggleCategory = (categoryId) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  const toggleSubcategory = (subcategoryId) => {
    setExpandedSubcategories(prev => ({
      ...prev,
      [subcategoryId]: !prev[subcategoryId]
    }));
  };

  const handleDragEnd = async (event) => {
    const { active, over } = event;
    setIsDragging(false);

    if (!over || active.id === over.id) {
      return;
    }

    const activeId = active.id;
    const overId = over.id;

    // Check if we're dragging categories
    if (activeId.startsWith('category-') && overId.startsWith('category-')) {
      const oldIndex = categories.findIndex(cat => `category-${cat._id}` === activeId);
      const newIndex = categories.findIndex(cat => `category-${cat._id}` === overId);

      if (oldIndex !== -1 && newIndex !== -1) {
        const newCategories = arrayMove(categories, oldIndex, newIndex);

        // Update local state immediately for better UX
        setCategories(newCategories);

        // Prepare category orders for backend
        const categoryOrders = newCategories.map((category, index) => ({
          categoryId: category._id,
          position: index
        }));

        try {
          await reorderCategories(restaurantData._id, categoryOrders);
          toast.success("Categories reordered successfully");
        } catch (error) {
          toast.error("Failed to reorder categories");
          // Revert the change on error
          const revertedCategories = arrayMove(newCategories, newIndex, oldIndex);
          setCategories(revertedCategories);
        }
      }
    }

    // Check if we're dragging items within a category
    if (activeId.startsWith('item-') && overId.startsWith('item-')) {
      const activeItemId = activeId.replace('item-', '');
      const overItemId = overId.replace('item-', '');

      // Find which category/subcategory contains these items
      let targetCategory = null;
      let targetSubcategory = null;
      let itemsArray = null;

      for (const category of categories) {
        // Check main category items
        const activeInMain = category.items.find(item => item._id === activeItemId);
        const overInMain = category.items.find(item => item._id === overItemId);

        if (activeInMain && overInMain) {
          targetCategory = category;
          itemsArray = category.items;
          break;
        }

        // Check subcategory items
        for (const subcategory of category.subcategories || []) {
          const activeInSub = subcategory.items.find(item => item._id === activeItemId);
          const overInSub = subcategory.items.find(item => item._id === overItemId);

          if (activeInSub && overInSub) {
            targetCategory = category;
            targetSubcategory = subcategory;
            itemsArray = subcategory.items;
            break;
          }
        }

        if (itemsArray) break;
      }

      if (itemsArray) {
        const oldIndex = itemsArray.findIndex(item => item._id === activeItemId);
        const newIndex = itemsArray.findIndex(item => item._id === overItemId);

        if (oldIndex !== -1 && newIndex !== -1) {
          const newItems = arrayMove(itemsArray, oldIndex, newIndex);

          // Update local state
          const updatedCategories = categories.map(cat => {
            if (cat._id === targetCategory._id) {
              if (targetSubcategory) {
                return {
                  ...cat,
                  subcategories: cat.subcategories.map(sub =>
                    sub._id === targetSubcategory._id
                      ? { ...sub, items: newItems }
                      : sub
                  )
                };
              } else {
                return { ...cat, items: newItems };
              }
            }
            return cat;
          });

          setCategories(updatedCategories);

          // Prepare item orders for backend
          const itemOrders = newItems.map((item, index) => ({
            itemId: item._id,
            position: index
          }));

          try {
            await reorderItems(itemOrders);
            toast.success("Items reordered successfully");
          } catch (error) {
            toast.error("Failed to reorder items");
            // Revert the change on error
            const revertedItems = arrayMove(newItems, newIndex, oldIndex);
            const revertedCategories = categories.map(cat => {
              if (cat._id === targetCategory._id) {
                if (targetSubcategory) {
                  return {
                    ...cat,
                    subcategories: cat.subcategories.map(sub =>
                      sub._id === targetSubcategory._id
                        ? { ...sub, items: revertedItems }
                        : sub
                    )
                  };
                } else {
                  return { ...cat, items: revertedItems };
                }
              }
              return cat;
            });

            setCategories(revertedCategories);
          }
        }
      }
    }
  };

  const handleDragStart = () => {
    setIsDragging(true);
  };

  useEffect(() => {
    async function fetchAllCategories() {
      try {
        if (!restaurantData || !restaurantData._id) {
          return;
        }
        let restaurantId = restaurantData._id;
        let response = await getAllCategories(restaurantId);
        if (response.data.success) {
          setCategories(response.data.categories);
        }
      } catch (error) {
        console.log(error);
      }
    }
    fetchAllCategories();
  }, [restaurantData]);

  return (
    <div className="w-full min-h-screen bg-gray-50 p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <h1 className="text-2xl sm:text-3xl font-semibold text-gray-800">
            Manage Menu
          </h1>
          <Button
            onClick={() => handleOpenDialog('Category')}
            className="w-full sm:w-auto bg-[#22C55E] hover:bg-[#1ea952] text-white px-4 sm:px-6 py-2.5 rounded-lg flex items-center justify-center gap-2 font-medium transition-all duration-200"
          >
            <RiAddLine className="text-xl" />
            Add Category
          </Button>
        </div>

        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
          onDragStart={handleDragStart}
        >
          <SortableContext
            items={categories.map(cat => `category-${cat._id}`)}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-4">
              {categories.map((category) => (
                <SortableCategory
                  key={category._id}
                  category={category}
                  isDragging={isDragging}
                  toggleCategory={toggleCategory}
                  expandedCategories={expandedCategories}
                  expandedSubcategories={expandedSubcategories}
                  toggleSubcategory={toggleSubcategory}
                  handleAddItem={handleAddItem}
                  handleOpenDialog={handleOpenDialog}
                  handleEditItem={handleEditItem}
                  handleDeleteItem={handleDeleteItem}
                  handleDeleteSubcategory={handleDeleteSubcategory}
                  handleAddExtra={handleAddExtra}
                  handleDeleteExtra={handleDeleteExtra}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>

      {/* Existing dialog and modal components */}
      <DialogForm
        open={isDialogOpen}
        onClose={handleCloseDialog}
        DialogType={DialogType}
        initialFormValue={initialFormValue}
        handleDialogFormDataSubmit={handleDialogFormDataSubmit}
      />

      <AddItemModal
        isOpen={isItemModalOpen}
        onClose={() => setIsItemModalOpen(false)}
        onSave={handleSaveItem}
        categoryId={currentCategoryId}
        subcategoryId={currentSubcategoryId}
        editItem={editingItem}
        loading={loading}
        setLoading={setLoading}
      />

      <AddExtraModal
        isOpen={isExtraModalOpen}
        onClose={() => setIsExtraModalOpen(false)}
        onSave={handleSaveExtra}
        loading={loading}
        setLoading={setLoading}
      />
      </div>
    </div>
  );
};

export default RMenu;