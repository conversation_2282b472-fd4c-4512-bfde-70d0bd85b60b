@tailwind base;
@tailwind components;
@tailwind utilities;
@layer base {
  :root {
    --radius: 0.5rem;
  }
}

/* Add this to your custom CSS file */
.scrollbar-thin {
  scrollbar-width: thin; /* For Firefox */
}

.scrollbar-thinner::-webkit-scrollbar {
  width: 4px; /* For WebKit-based browsers (e.g., Chrome) */
}

.scrollbar-thinner::-webkit-scrollbar-thumb {
  background: #4b5563; /* Thumb color */
  border-radius: 8px;
}

.scrollbar-thinner::-webkit-scrollbar-track {
  background: #1f2937; /* Track color */
}

/* hide inpute tupe number arrow */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
