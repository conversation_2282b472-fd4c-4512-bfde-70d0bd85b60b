const Item = require('../models/item.model');
const Category = require('../models/category.model');
const Subcategory = require('../models/subcategory.model');
const { validateItem } = require('../validators/itemValidator');

// Create a New Item
exports.createItem = async (req, res, next) => {
  try {
    // Validate input data using Joi
    const { error } = validateItem(req.body);
    if (error) {
      return res.status(400).json({ message: error.details[0].message, success: false });
    }

    const { categoryId, subcategoryId, title, detail, price, type, menuButton, extras } = req.body;

    // Check if item with same title already exists in the category/subcategory
    let existingItem;
    if (categoryId) {
      existingItem = await Item.findOne({
        title,
        categoryId: categoryId
      });
    } else if (subcategoryId) {
      existingItem = await Item.findOne({
        title,
        subcategoryId: subcategoryId
      });
    }

    if (existingItem) {
      return res.status(400).json({
        message: 'An item with this title already exists in this category or subcategory',
        success: false
      });
    }

    // Extract Cloudinary URL from uploaded file
    const subimage = req.file ? req.file.path : null;

    // Create a new item
    const newItem = new Item({
      categoryId,
      subcategoryId,
      title,
      detail,
      price,
      type,
      subimage,
      menuButton,
      extras,
    });

    const savedItem = await newItem.save();

    // Add the item reference to category or subcategory
    if (subcategoryId) {
      await Subcategory.findByIdAndUpdate(subcategoryId, { $push: { items: savedItem._id } });
    } else if (categoryId) {
      await Category.findByIdAndUpdate(categoryId, { $push: { items: savedItem._id } });
    }

    res.status(201).json({ message: 'Item created successfully', item: savedItem, success: true });
  } catch (error) {
    console.log(error);
    next(error);
  }
};

// Update an Existing Item
exports.updateItem = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { error } = validateItem(req.body);
    if (error) {
      return res.status(400).json({ message: error.details[0].message, success: false });
    }

    const { title, detail, price, type, menuButton, extras, categoryId, subcategoryId } = req.body;

    const item = await Item.findById(id);
    if (!item) {
      return res.status(404).json({ message: 'Item not found', success: false });
    }

    // Check if item with same title already exists in the category/subcategory
    if (title && title !== item.title) {
      const existingItem = await Item.findOne({
        title,
        _id: { $ne: id }, // Exclude current item
        $or: [
          { categoryId: categoryId || item.categoryId },
          { subcategoryId: subcategoryId || item.subcategoryId }
        ]
      });

      if (existingItem) {
        return res.status(400).json({
          message: 'An item with this title already exists in this category or subcategory',
          success: false
        });
      }
    }

    // Extract Cloudinary URL from uploaded file for updated image
    if (req.file) {
      item.subimage = req.file.path;
    }

    // Update item fields
    item.title = title || item.title;
    item.detail = detail || item.detail;
    item.price = price || item.price;
    item.type = type || item.type;
    item.menuButton = menuButton !== undefined ? menuButton : item.menuButton;
    item.extras = extras || item.extras;
    item.categoryId = categoryId || item.categoryId;
    item.subcategoryId = subcategoryId || item.subcategoryId;

    const updatedItem = await item.save();

    res.status(200).json({ message: 'Item updated successfully', item: updatedItem, success: true });
  } catch (error) {
    next(error);
  }
};

// Get a single item by ID
exports.getItemById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const item = await Item.findById(id);

    if (!item) {
      return res.status(404).json({ message: 'Item not found', success: false });
    }

    return res.status(200).json(item);
  } catch (error) {
    next(error);
  }
};

// Get all items
exports.getAllItems = async (req, res, next) => {
  try {
    const { categoryId, subcategoryId } = req.query;

    // Don't allow both categoryId and subcategoryId
    if (categoryId && subcategoryId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot filter by both category and subcategory. Please provide only one or remove both'
      });
    }

    // Build query dynamically
    const query = {};
    if (categoryId) query.categoryId = categoryId;
    if (subcategoryId) query.subcategoryId = subcategoryId;

    // Fetch items based on query or fetch all items
    const items = await Item.find(query).populate('extras');

    return res.status(200).json({ success: true, items });
  } catch (error) {
    next(error);
  }
};

// Delete an item by ID
exports.deleteItem = async (req, res, next) => {
  try {
    const { id } = req.params;
    const item = await Item.findById(id);

    if (!item) {
      return res.status(404).json({ message: 'Item not found', success: false });
    }

    // Remove item reference from subcategory or category
    if (item.subcategoryId) {
      await Subcategory.findByIdAndUpdate(item.subcategoryId, { $pull: { items: item._id } });
    } else if (item.categoryId) {
      await Category.findByIdAndUpdate(item.categoryId, { $pull: { items: item._id } });
    }

    await item.deleteOne();
    return res.status(200).json({ message: 'Item deleted successfully', success: true });
  } catch (error) {
    next(error);
  }
};

// Add extra's to an item
exports.addExtraToItem = async (req, res, next) => {
  try {
    const { id } = req.params; // Item ID
    const { title, price } = req.body; // Extract title and price directly

    // Validate the input
    if (!title || price === undefined) {
      return res.status(400).json({ success: false, message: 'Title and price are required' });
    }

    // Find the item
    const item = await Item.findById(id);

    if (!item) {
      return res.status(404).json({ success: false, message: 'Item not found' });
    }

    // Check if an extra with the same title already exists
    const extraExists = item.extras.some(existingExtra =>
      existingExtra.title.toLowerCase() === title.toLowerCase()
    );

    if (extraExists) {
      return res.status(400).json({
        success: false,
        message: 'An extra with this title already exists for this item'
      });
    }

    // Push the new extra into the extras array
    const updatedItem = await Item.findByIdAndUpdate(
      id,
      { $push: { extras: { title, price } } }, // Add the new extra object
      { new: true }
    );

    res.status(200).json({ success: true, message: 'Extra added successfully', item: updatedItem });
  } catch (error) {
    next(error);
  }
};

//remove extra from item 
exports.removeExtraFromItem = async (req, res, next) => {
  try {
    const { id } = req.params; // Item ID
    const { extraId } = req.body; // ID of the extra to be removed

    // Validate extraId
    if (!extraId) {
      return res.status(400).json({ success: false, message: 'Extra ID is required' });
    }

    // Pull the extra from the extras array
    const updatedItem = await Item.findByIdAndUpdate(
      id,
      { $pull: { extras: { _id: extraId } } }, // Remove the extra by its _id
      { new: true }
    );

    if (!updatedItem) {
      return res.status(404).json({ success: false, message: 'Item not found' });
    }

    res.status(200).json({ success: true, message: 'Extra removed successfully', item: updatedItem });
  } catch (error) {
    next(error);
  }
};



