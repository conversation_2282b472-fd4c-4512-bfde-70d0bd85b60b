require('dotenv').config();
const multer = require('multer');
const path = require('path');
const fs = require('fs');


// Load environment variables
const uploadsDir = path.join(__dirname, `../${process.env.UPLOADS_DIR || 'uploads'}`);
const maxFileSize = parseInt(process.env.MAX_FILE_SIZE, 10) || 2 * 1024 * 1024; //default 2MB
const allowedFileTypes = (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png').split(',');

// Ensure the uploads directory exists
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Multer storage configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, uniqueSuffix + '-' + file.originalname);
  },
});

// File type filter
const fileFilter = (req, file, cb) => {
  if (allowedFileTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Unsupported file type'), false);
  }
};

// Multer configuration
const upload = multer({
  storage,
  limits: { fileSize: maxFileSize },
  fileFilter,
});

module.exports = {
  upload,
};
