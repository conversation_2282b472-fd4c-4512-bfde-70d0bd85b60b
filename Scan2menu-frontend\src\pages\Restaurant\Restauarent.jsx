import React, { useState, useEffect, useContext } from 'react';
import { RestaurantDataContext } from '../../context/RestaurantContext';
import InputField from '../../components/InputFieldValidation/InputField';
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from '../../components/ui/select';
import { toast } from 'react-hot-toast';
import { postRestaurantData, updateRestaurantData } from '../../api';
import { Button } from '../../components/ui/button';
import { Loader2, Upload } from 'lucide-react';

const Restaurant = () => {
  const { restaurantData, setRestaurantData } = useContext(RestaurantDataContext);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    subTitle: '',
    timing: '',
    description: '',
    location: '',
    gstID: '',
    image: null,
    coverImage: null,
    allowOnTableOrder: '',
    allowTAX: '',
    taxPercentage: '',
    numberOfTables: 0,  // Add this line
  });

  const activeGradientClass = "bg-gradient-to-br from-[#E28E66] to-[#F2A44F]";
  const hoverGradientClass = "hover:from-[#d68660] hover:to-[#e69847]";

  const validate = () => {
    const newError = {};

    if (!formData.name) {
      newError.name = 'Name is required'
    } else if (formData.name.length < 3) {
      newError.name = 'Name must be at least 3 characters long';
    } else if (formData.name.length > 100) {
      newError.name = 'Name must be at most 100 characters long';
    }
    if (!formData.slug) {
      newError.slug = 'Slug is required';
    } else if (!/^[a-z]+(-[a-z]+)*$/.test(formData.slug)) {
      newError.slug = 'Slug can only contain lowercase letters and hyphens (-)';
    } else if (formData.slug.length < 3) {
      newError.slug = 'Slug must be at least 3 characters long';
    } else if (formData.slug.length > 100) {
      newError.slug = 'Slug must be at most 100 characters long';
    }
    if (!formData.subTitle) {
      newError.subTitle = 'Sub Title is required'
    } else if (formData.subTitle.length > 150) {
      newError.subTitle = 'Sub Title must be at most 150 characters long';
    }
    if (!formData.timing) {
      newError.timing = 'Timing is required'
    } else if (formData.timing.length < 5) {
      newError.timing = 'Timing must be at least 5 characters long';
    }
    if (!formData.description) {
      newError.description = 'Description is required'
    } else if (formData.description.length > 500) {
      newError.description = 'Description must be at most 500 characters long';
    }
    if (!formData.location) {
      newError.location = 'Location is required'
    } else if (formData.location.length < 5) {
      newError.location = 'Location must be at least 5 characters long';
    } else if (formData.location.length > 200) {
      newError.location = 'Location must be at most 200 characters long';
    }
    if (!formData.gstID) {
      newError.gstID = 'GST number is required'
    } else if (!/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(formData.gstID)) {
      newError.gstID = 'GST number can only contain lowercase letters and hyphens (-)';
    }
    if (!restaurantData) {
      if (!formData.image) newError.image = 'Image is required';
      if (!formData.coverImage) newError.coverImage = 'Cover Image is required';
    }
    if (formData.allowOnTableOrder === '') newError.allowOnTableOrder = 'Allow On Table Order is required';
    if (formData.allowTAX === '') newError.allowTAX = 'Allow TAX is required';
    if (formData.allowTAX === "yes" && !formData.taxPercentage) newError.taxPercentage = 'Tax Percentage is required';
    if (!formData.numberOfTables) {
      newError.numberOfTables = 'Number of Tables is required';
    } else if (formData.numberOfTables < 1) {
      newError.numberOfTables = 'Number of Tables must be at least 1';
    } else if (formData.numberOfTables > 100) {
      newError.numberOfTables = 'Number of Tables cannot exceed 100';
    }

    setErrors(newError);
    return Object.keys(newError).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value, files } = e.target;
    setFormData({
      ...formData,
      [name]: files ? files[0] : value,
    });
    setErrors({
      ...errors, [name]: ''
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    console.log(errors);
    if (!validate()) return;

    try {
      setLoading(true);
      const payload = new FormData();

      payload.append('name', formData.name);
      payload.append('slug', formData.slug)
      payload.append('subTitle', formData.subTitle);
      payload.append('timing', formData.timing);
      payload.append('description', formData.description);
      payload.append('location', formData.location);
      payload.append('gstID', formData.gstID);

      if (formData.image instanceof File) {
        payload.append('image', formData.image);
      }

      if (formData.coverImage instanceof File) {
        payload.append('coverImage', formData.coverImage);
      }

      payload.append('allowOnTableOrder', formData.allowOnTableOrder);
      payload.append('allowTAX', formData.allowTAX);
      payload.append('taxPercentage', formData.allowTAX === "yes" ? formData.taxPercentage : 0);
      payload.append('numberOfTables', formData.numberOfTables);

      const response = restaurantData
        ? await updateRestaurantData(payload, restaurantData._id) // Call update API
        : await postRestaurantData(payload); // Call create API

      if (response.data.success) {
        setRestaurantData(response.data.restaurant);
        toast.success(response.data.message);
      }
    } catch (error) {
      toast.error(error.response?.data?.message || error.message);
    } finally {
      setLoading(false);
    }

  };

  useEffect(() => {
    if (restaurantData) {
      setFormData({
        name: restaurantData.name,
        slug: restaurantData.slug,
        subTitle: restaurantData.subTitle,
        timing: restaurantData.timing,
        description: restaurantData.description,
        location: restaurantData.location,
        gstID: restaurantData.gstID,
        image: restaurantData.image || null,
        coverImage: restaurantData.coverImage || null,
        allowOnTableOrder: restaurantData.allowOnTableOrder,
        allowTAX: restaurantData.allowTAX,
        taxPercentage: restaurantData.taxPercentage,
        numberOfTables: restaurantData.numberOfTables || '',
      });
    }
  }, [restaurantData]);
  return (
    <div className="w-full min-h-screen bg-gray-50/50">
      <div className="p-6">
        <div className="mb-8">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-[#E28E66] to-[#F2A44F] bg-clip-text text-transparent">
            Restaurant Configuration
          </h1>
          <p className="text-gray-600 mt-2">Configure your restaurant details and preferences</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information Card */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h2 className="text-lg font-semibold mb-6">Basic Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <InputField
                label="Restaurant Name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                error={errors.name}
                placeholder="Enter your restaurant name"
                className="w-full"
              />

              <InputField
                label="Slug"
                name="slug"
                value={formData.slug}
                onChange={handleInputChange}
                error={errors.slug}
                placeholder="your-restaurant-name"
                disabled={!!restaurantData}
                className="w-full"
              />
            </div>
          </div>

          {/* Details Card */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h2 className="text-lg font-semibold mb-6">Restaurant Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-6">
                <InputField
                  label="Sub Title"
                  name="subTitle"
                  value={formData.subTitle}
                  onChange={handleInputChange}
                  error={errors.subTitle}
                  placeholder="A brief tagline for your restaurant"
                  className="w-full"
                />

                <InputField
                  label="Timing"
                  name="timing"
                  value={formData.timing}
                  onChange={handleInputChange}
                  error={errors.timing}
                  placeholder="e.g., Mon-Sun: 9:00 AM - 10:00 PM"
                  className="w-full"
                />

                <InputField
                  label="Location"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  error={errors.location}
                  placeholder="Full address of your restaurant"
                  className="w-full"
                />



              </div>
              <div className='flex flex-col justify-between'>
                <InputField
                  label="GST Number"
                  name="gstID"
                  value={formData.gstID}
                  onChange={handleInputChange}
                  error={errors.gstID}
                  placeholder="Enter your GST number"
                  className="w-full"
                />

                <InputField
                  label="Description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  error={errors.description}
                  placeholder="Tell customers about your restaurant..."
                  multiline
                  rows={5}
                />
              </div>
            </div >
          </div>

          {/* Images Card */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h2 className="text-lg font-semibold mb-6">Restaurant Images</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Restaurant Image */}
              <div className={`border-2 border-dashed border-gray-200 rounded-xl p-6 text-center hover:border-[#E28E66] transition-colors  ${errors.image ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : ''}`}>
                <input
                  type="file"
                  name="image"
                  onChange={handleInputChange}
                  className="hidden"
                  id="restaurant-image"
                  accept='image/jpeg, image/png, image/jpg'
                />
                <label htmlFor="restaurant-image" className="cursor-pointer">
                  {formData.image ? (
                    <img
                      src={formData.image instanceof File ? URL.createObjectURL(formData.image) : formData.image}
                      alt="Restaurant"
                      className="w-full h-48 object-cover rounded-lg mb-4"
                    />
                  ) : (
                    <div className="h-48 flex items-center justify-center">
                      <div className="text-center">
                        <Upload className="w-12 h-12 mx-auto text-gray-400 mb-2" />
                        <span className="text-gray-500">Upload Restaurant Image</span>
                      </div>
                    </div>
                  )}
                </label>
                {errors.image && <p className="text-red-500 text-sm mt-2">{errors.image}</p>}
              </div>

              {/* Cover Image */}
              <div
                className={`border-2 border-dashed rounded-xl p-6 text-center hover:border-[#E28E66] transition-colors  
  ${errors.coverImage ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-200'}`}
              >


                <input
                  type="file"
                  name="coverImage"
                  onChange={handleInputChange}
                  className="hidden"
                  id="cover-image"
                  accept='image/jpeg, image/png, image/jpg'

                />
                <label htmlFor="cover-image" className="cursor-pointer">
                  {formData.coverImage ? (
                    <img
                      src={formData.coverImage instanceof File ? URL.createObjectURL(formData.coverImage) : formData.coverImage}
                      alt="Cover"
                      className="w-full h-48 object-cover rounded-lg mb-4"
                    />
                  ) : (
                    <div className="h-48 flex items-center justify-center">
                      <div className="text-center">
                        <Upload className="w-12 h-12 mx-auto text-gray-400 mb-2" />
                        <span className="text-gray-500">Upload Cover Image</span>
                      </div>
                    </div>
                  )}
                </label>
                {errors.coverImage && <p className="text-red-500 text-sm mt-2">{errors.coverImage}</p>}
              </div>
            </div>
          </div>

          {/* Settings Card */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h2 className="text-lg font-semibold mb-6">Restaurant Settings</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Allow On Table Order</label>
                  <Select
                    name="allowOnTableOrder"
                    onValueChange={(value) => {
                      setFormData({ ...formData, allowOnTableOrder: value });
                      setErrors({ ...errors, allowOnTableOrder: '' });
                    }}
                    value={formData.allowOnTableOrder}

                  >
                    <SelectTrigger className={`w-full ${errors.allowOnTableOrder ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-200'}`}>
                      <SelectValue placeholder="Select option" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="yes">Yes</SelectItem>
                      <SelectItem value="no">No</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.allowOnTableOrder && <p className="text-red-500 text-sm mt-1">{errors.allowOnTableOrder}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Allow TAX</label>
                  <Select
                    name="allowTAX"
                    onValueChange={(value) => {
                      setFormData({ ...formData, allowTAX: value });
                      setErrors({ ...errors, allowTAX: '' });
                    }}
                    value={formData.allowTAX}
                  >
                    <SelectTrigger className={`w-full ${errors.allowTAX ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-200'}`}>
                      <SelectValue placeholder="Select option" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="yes">Yes</SelectItem>
                      <SelectItem value="no">No</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.allowTAX && <p className="text-red-500 text-sm mt-1">{errors.allowTAX}</p>}
                </div>

                {formData.allowTAX === 'yes' && (
                  <InputField
                    label="Tax Percentage"
                    name="taxPercentage"
                    value={formData.taxPercentage}
                    onChange={handleInputChange}
                    error={formData.allowTAX ? errors.taxPercentage : ''}
                    placeholder="Enter tax percentage"
                    type="number"
                  />
                )}
                <InputField
                  label="Number of Tables"
                  name="numberOfTables"
                  value={formData.numberOfTables}
                  onChange={handleInputChange}
                  error={errors.numberOfTables}
                  placeholder="Enter number of tables"
                  type="number"
                  min="1"
                  max="100"
                  className="w-full"
                />
              </div>

              <div className="flex items-end justify-end">
                {loading ? (
                  <Button disabled className="w-full md:w-1/2 h-12">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Please wait
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    className={`w-full md:w-1/2 h-12 text-white shadow-lg shadow-[#E28E66]/25 
                      ${activeGradientClass} ${hoverGradientClass} transition-all duration-300`}
                  >
                    {restaurantData ? 'Update Restaurant' : 'Create Restaurant'}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </form>
      </div >
    </div >
  );
};

export default Restaurant;
