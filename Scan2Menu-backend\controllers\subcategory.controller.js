const Subcategory = require('../models/subcategory.model');
const Category = require('../models/category.model');
const { subcategorySchema } = require('../validators/subcategoryValidator');

// Add a new subcategory
exports.addSubcategory = async (req, res, next) => {
  try {
    // Validate input data using Joi
    const { error } = subcategorySchema.validate(req.body);
    if (error) {
      return res.status(400).json({ message: error.details[0].message, success: false });
    }

    const { categoryId, subcategoryName } = req.body;

    // Check if the subcategory already exists for the given category
    const existingSubcategory = await Subcategory.findOne({ categoryId, subcategoryName });
    if (existingSubcategory) {
      return res.status(400).json({
        message: 'Subcategory with this title already exists for the category.',
        success: false,
      });
    }

    const subcategory = new Subcategory(req.body);
    const savedSubcategory = await subcategory.save();

    // Update the parent category to include the subcategory reference
    await Category.findByIdAndUpdate(categoryId, {
      $push: { subcategories: savedSubcategory._id },
    });

    return res.status(201).json({
      message: 'Subcategory added successfully',
      subcategory: savedSubcategory,
      success: true,
    });
  } catch (error) {
    next(error);
  }
};

// Get all subcategories of a category
exports.getAllSubcategoriesByCategory = async (req, res, next) => {
  try {
    const { categoryId } = req.params;
    if (!categoryId) {
      return res
        .status(400)
        .json({ message: 'Category ID not provided', success: false });
    }

    const subcategories = await Subcategory.find({ categoryId }).populate('items');

    if (!subcategories.length) {
      return res
        .status(404)
        .json({ message: 'No subcategories found for this category', success: false });
    }

    return res.status(200).json(subcategories);
  } catch (error) {
    next(error);
  }
};

// Get a single subcategory by ID
exports.getSingleSubcategory = async (req, res, next) => {
  try {
    const { id } = req.params;
    if (!id) {
      return res
        .status(400)
        .json({ message: 'Subcategory ID not provided', success: false });
    }

    const subcategory = await Subcategory.findById(id).populate('items');

    if (!subcategory) {
      return res
        .status(404)
        .json({ message: 'Subcategory not found', success: false });
    }

    return res.status(200).json(subcategory);
  } catch (error) {
    next(error);
  }
};

// Update a subcategory by ID
exports.updateSubcategory = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Check if subcategory exists
    const existingSubcategory = await Subcategory.findById(id);
    if (!existingSubcategory) {
      return res
        .status(404)
        .json({ message: 'Subcategory not found', success: false });
    }

    // If subcategoryName is being updated, check if it already exists
    if (updates.subcategoryName) {
      const duplicateSubcategory = await Subcategory.findOne({
        categoryId: existingSubcategory.categoryId,
        subcategoryName: updates.subcategoryName,
        _id: { $ne: id } // Exclude current subcategory
      });

      if (duplicateSubcategory) {
        return res
          .status(400)
          .json({ message: 'Subcategory name already exists in this category', success: false });
      }
    }

    const updatedSubcategory = await Subcategory.findByIdAndUpdate(id, updates, {
      new: true,
    });

    return res
      .status(200)
      .json({ message: 'Subcategory updated successfully', subcategory: updatedSubcategory, success: true });
  } catch (error) {
    next(error);
  }
};

// Delete a subcategory by ID
exports.deleteSubcategory = async (req, res, next) => {
  try {
    const { id } = req.params;

    const subcategory = await Subcategory.findById(id);
    if (!subcategory) {
      return res
        .status(404)
        .json({ message: 'Subcategory not found', success: false });
    }

    // Remove the subcategory reference from the parent category
    await Category.findByIdAndUpdate(subcategory.categoryId, {
      $pull: { subcategories: subcategory._id },
    });

    await subcategory.deleteOne();

    return res.status(200).json({
      message: 'Subcategory deleted successfully',
      success: true,
      subcategory: subcategory
    });
  } catch (error) {
    next(error);
  }
};

// Reorder subcategories within a category for drag and drop functionality
exports.reorderSubcategories = async (req, res, next) => {
  try {
    const { categoryId } = req.params;
    const { subcategoryOrders } = req.body; // Array of {subcategoryId, position}

    if (!subcategoryOrders || !Array.isArray(subcategoryOrders)) {
      return res.status(400).json({
        message: "subcategoryOrders must be an array",
        success: false
      });
    }

    // Update positions for all subcategories
    const updatePromises = subcategoryOrders.map(({ subcategoryId, position }) =>
      Subcategory.findByIdAndUpdate(subcategoryId, { position }, { new: true })
    );

    await Promise.all(updatePromises);

    return res.status(200).json({
      message: "Subcategories reordered successfully",
      success: true
    });
  } catch (error) {
    next(error);
  }
};

