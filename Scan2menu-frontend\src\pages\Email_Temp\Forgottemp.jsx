import React from 'react';

const ForgotPasswordEmail = ({ resetLink, userName }) => {
  return (
    <div style={{
      fontFamily: 'Arial, sans-serif',
      maxWidth: '600px',
      margin: '0 auto',
      padding: '20px',
      backgroundColor: '#ffffff',
      borderRadius: '8px',
      border: '1px solid #e4e4e4'
    }}>
      {/* Header with Logo */}
      <div style={{
        textAlign: 'center',
        padding: '20px 0',
        borderBottom: '1px solid #e4e4e4'
      }}>
        <img
          src="https://your-domain.com/logo.png"
          alt="Scan2Menu Logo"
          style={{
            height: '50px',
            width: 'auto'
          }}
        />
      </div>

      {/* Email Content */}
      <div style={{
        padding: '30px 20px',
        color: '#333333'
      }}>
        <h1 style={{
          fontSize: '24px',
          marginBottom: '20px',
          color: '#222222'
        }}>Reset Your Password</h1>

        <p style={{
          fontSize: '16px',
          lineHeight: '1.5',
          marginBottom: '20px'
        }}>
          Hello {userName || 'there'},
        </p>

        <p style={{
          fontSize: '16px',
          lineHeight: '1.5',
          marginBottom: '20px'
        }}>
          We received a request to reset your password for your Scan2Menu account. If you didn't make this request, you can safely ignore this email.
        </p>

        <p style={{
          fontSize: '16px',
          lineHeight: '1.5',
          marginBottom: '30px'
        }}>
          To reset your password, please click the button below:
        </p>

        {/* Reset Password Button */}
        <div style={{
          textAlign: 'center',
          margin: '30px 0'
        }}>
          <a href={resetLink} style={{
            backgroundColor: '#4CAF50',
            color: 'white',
            padding: '12px 30px',
            textDecoration: 'none',
            borderRadius: '4px',
            fontWeight: 'bold',
            display: 'inline-block',
            fontSize: '16px'
          }}>
            Reset Password
          </a>
        </div>

        <p style={{
          fontSize: '16px',
          lineHeight: '1.5',
          marginBottom: '20px'
        }}>
          If the button doesn't work, copy and paste the following link into your browser:
        </p>

        <p style={{
          fontSize: '14px',
          backgroundColor: '#f5f5f5',
          padding: '10px',
          borderRadius: '4px',
          wordBreak: 'break-all',
          marginBottom: '30px'
        }}>
          {resetLink || 'https://scan2menu.com/reset-password?token=your-reset-token'}
        </p>

        <p style={{
          fontSize: '16px',
          lineHeight: '1.5',
          marginBottom: '10px'
        }}>
          This link will expire in 24 hours for security reasons.
        </p>

        <p style={{
          fontSize: '16px',
          lineHeight: '1.5',
          marginBottom: '30px'
        }}>
          If you need any assistance, please contact our support team.
        </p>

        <p style={{
          fontSize: '16px',
          lineHeight: '1.5',
          marginBottom: '10px'
        }}>
          Best regards,
        </p>

        <p style={{
          fontSize: '16px',
          lineHeight: '1.5',
          fontWeight: 'bold'
        }}>
          The Scan2Menu Team
        </p>
      </div>

      {/* Footer */}
      <div style={{
        padding: '20px',
        textAlign: 'center',
        borderTop: '1px solid #e4e4e4',
        fontSize: '14px',
        color: '#666666'
      }}>
        <p style={{ marginBottom: '10px' }}>
          &copy; {new Date().getFullYear()} Scan2Menu. All rights reserved.
        </p>
        <p style={{ marginBottom: '10px' }}>
          123 Restaurant Avenue, Food City, FC 12345
        </p>
        <div>
          <a href="https://scan2menu.com/privacy" style={{ color: '#4CAF50', marginRight: '10px', textDecoration: 'none' }}>
            Privacy Policy
          </a>
          <a href="https://scan2menu.com/terms" style={{ color: '#4CAF50', textDecoration: 'none' }}>
            Terms of Service
          </a>
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordEmail;