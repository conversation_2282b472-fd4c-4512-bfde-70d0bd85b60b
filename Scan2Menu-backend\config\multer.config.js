require('dotenv').config();
const multer = require('multer');
const { CloudinaryStorage } = require('multer-storage-cloudinary');
const cloudinary = require('./cloudinary.config'); // Import Cloudinary instance

const storage = new CloudinaryStorage({
  cloudinary,
  params: async (req, file) => {
    // Determine folder based on the type of image (e.g., restaurant image, cover image)
    let subFolder = '';
    if (file.fieldname === 'image' || file.fieldname === 'coverImage') {
      subFolder = 'Restaurant'; // All restaurant-related images are uploaded to this folder
    }
    if (file.fieldname === 'subimage') {
      subFolder = 'Menu'; // All restaurant-related images are uploaded to this folder
    }
    console.log(file);
    // Return folder path, file format, and public_id (unique name for the image)
    return {
      folder: `${process.env.CLOUDINARY_MAIN_FOLDER || 'SCAN2MENU'}/${subFolder}`,
      allowed_formats: ['jpg', 'jpeg', 'png']
    };
  },
});

const upload = multer({ storage });

module.exports = {
  upload,
};
