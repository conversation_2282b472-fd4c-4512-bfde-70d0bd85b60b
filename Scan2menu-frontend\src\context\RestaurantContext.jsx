import { useEffect, createContext, useState } from "react";

export const RestaurantDataContext = createContext();

const RestaurantContext = ({ children }) => {
    const [restaurantData, setRestaurantData] = useState(null);

    useEffect(() => {
        const storedRestaurant = localStorage.getItem('restaurantData');
        if (storedRestaurant) {
            setRestaurantData(JSON.parse(storedRestaurant));
        }
    }, []);

    useEffect(() => {
        if (restaurantData) {
            localStorage.setItem('restaurantData', JSON.stringify(restaurantData));
        } else {
            localStorage.removeItem('restaurantData');
        }
    });


    return (
        <RestaurantDataContext.Provider value={{ restaurantData, setRestaurantData }}>
            {children}
        </RestaurantDataContext.Provider>
    )
    
}
export default RestaurantContext;