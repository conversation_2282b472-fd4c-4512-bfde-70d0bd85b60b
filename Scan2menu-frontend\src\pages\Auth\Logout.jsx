import React, { useContext, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { UserDataContext } from '../../context/UserContext';
import { RestaurantDataContext } from '../../context/RestaurantContext';
import { Button } from '../../components/ui/button';
import { postLogoutData } from '../../api';
import { toast } from 'react-hot-toast';

const Logout = () => {
    const { setUser } = useContext(UserDataContext);
    const { setRestaurantData } = useContext(RestaurantDataContext);
    const navigate = useNavigate();

    const handleLogout = async () => {

        try {
            let response = await postLogoutData();
            if (response.data.success) {
                setUser(null);
                setRestaurantData(null);
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                localStorage.removeItem('restaurantData');
                toast.success('Logged out successfully');
                navigate('/login');
            }
        } catch (error) {
            toast.error(error?.response?.data?.message || error?.message);
        }

    };

    return (
        <Button onClick={handleLogout}
            type="submit"
            className="w-full px-4 py-2 font-bold text-white bg-blue-500 rounded hover:bg-blue-700 focus:outline-none focus:ring focus:ring-blue-300"
        >
            Log out
        </Button>
    );
};

export default Logout;