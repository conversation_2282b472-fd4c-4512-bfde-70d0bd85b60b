const express = require('express');
const router = express.Router();
const { authMiddeleware } = require('../middleware/auth.middleware');
const tableController = require('../controllers/table.controller');


// Book table when order is created
router.put('/book/:tableId', authMiddeleware, tableController.bookTable);

// Update table status
router.put('/status/:tableId', authMiddeleware, tableController.updateTableStatus);

router.post('/initialize/:restaurantId', authMiddeleware, tableController.initializeTables);
// Get all tables for a restaurant
router.get('/:restaurantSlug', tableController.getAllTables);

module.exports = router; 