const Category = require("../models/category.model");
const Subcategory = require("../models/subcategory.model");
const { categorySchema } = require("../validators/categoryValidator");


exports.addcategory = async (req, res, next) => {
  try {
    // Validate input data using Jo<PERSON>
    const { error } = categorySchema.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ message: error.details[0].message, success: false });
    }

    const { restaurantId, categoryName, subcategories } = req.body;
    // Check if the category already exists for the given restaurant
    const existingCategory = await Category.findOne({ restaurantId, categoryName });

    if (existingCategory) {
      return res.status(400).json({ message: "Category with this name already exists for  restaurant.", success: false, });
    }

    const category = new Category({
      restaurantId,
      categoryName,
      subcategories: subcategories || [],
    });

    const savedCategory = await category.save();

    return res.status(201).json({ message: "Category added successfully", category: savedCategory, success: true, });
  } catch (error) {
    next(error);
  }
};

// Get all categories of the restaurant
exports.getallcatgory = async (req, res, next) => {
  try {
    const { restaurantId } = req.params;
    if (!restaurantId) {
      return res.status(404).json({ messgae: "Restaurant Id not provided" });
    }
    const categories = await Category.find({ restaurantId })
      .select("-restaurantId")
      .sort({ position: 1 }) // Sort categories by position
      .populate({
        path: 'items',
        options: { sort: { position: 1 } } // Sort items by position
      })
      .populate({
        path: "subcategories", // Populate subcategories
        options: { sort: { position: 1 } }, // Sort subcategories by position
        populate: {
          path: "items", // Populate items within each subcategory
          options: { sort: { position: 1 } } // Sort items by position
        }
      });

    return res.status(200).json({ categories, success: true });
  } catch (error) {
    next(error);
  }
};

exports.getsinglecategory = async (req, res, next) => {
  try {
    const { id } = req.params;
    if (!id) {
      return res.status(404).json({ message: "Category id not provided", success: false });
    }

    const category = await Category.findOne({ _id: id }).populate('subcategories');;

    if (!category) {
      return res.status(404).json({ message: "This category Isn't Exists", success: false });
    }

    return res.status(200).json(category);

  } catch (error) {
    next(error);
  }
}

exports.updatecategory = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { categoryName } = req.body;

    const updatecategory = await Category.findByIdAndUpdate(id, { categoryName }, { new: true });

    if (!updatecategory) {
      return res.status(404).json({ message: 'Category not found', success: true });
    }

    return res.status(200).json({ message: 'Category updated successfully', category: updatecategory, success: true });

  } catch (error) {
    next(error);
  }
}

exports.deletecategory = async (req, res, next) => {
  try {
    const { id } = req.params;

    const category = await Category.findById(id);
    if (!category) {
      return res.status(404).json({ message: "Category not Found", success: true });
    }

    // Then call deleteOne() to remove the category itself
    await category.deleteOne();

    return res.status(200).json({
      message: "Categorypo deleted Successfully",
      success: true
    });
  } catch (error) {
    next(error);
  }
}

// Reorder categories for drag and drop functionality
exports.reorderCategories = async (req, res, next) => {
  try {
    const { restaurantId } = req.params;
    const { categoryOrders } = req.body; // Array of {categoryId, position}

    if (!categoryOrders || !Array.isArray(categoryOrders)) {
      return res.status(400).json({
        message: "categoryOrders must be an array",
        success: false
      });
    }

    // Update positions for all categories
    const updatePromises = categoryOrders.map(({ categoryId, position }) =>
      Category.findByIdAndUpdate(categoryId, { position }, { new: true })
    );

    await Promise.all(updatePromises);

    return res.status(200).json({
      message: "Categories reordered successfully",
      success: true
    });
  } catch (error) {
    next(error);
  }
}