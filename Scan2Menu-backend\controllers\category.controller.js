const Category = require("../models/category.model");
const Subcategory = require("../models/subcategory.model");
const { categorySchema } = require("../validators/categoryValidator");


exports.addcategory = async (req, res, next) => {
  try {
    // Validate input data using Jo<PERSON>
    const { error } = categorySchema.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ message: error.details[0].message, success: false });
    }

    const { restaurantId, categoryName, subcategories } = req.body;
    // Check if the category already exists for the given restaurant
    const existingCategory = await Category.findOne({ restaurantId, categoryName });

    if (existingCategory) {
      return res.status(400).json({ message: "Category with this name already exists for  restaurant.", success: false, });
    }

    const category = new Category({
      restaurantId,
      categoryName,
      subcategories: subcategories || [],
    });

    const savedCategory = await category.save();

    return res.status(201).json({ message: "Category added successfully", category: savedCategory, success: true, });
  } catch (error) {
    next(error);
  }
};

// Get all categories of the restaurant
exports.getallcatgory = async (req, res, next) => {
  try {
    const { restaurantId } = req.params;
    if (!restaurantId) {
      return res.status(404).json({ messgae: "Restaurant Id not provided" });
    }
    const categories = await Category.find({ restaurantId })
      .select("-restaurantId")
      .populate('items')
      .populate({
        path: "subcategories", // Populate subcategories
        populate: {
          path: "items", // Populate items within each subcategory
        }
      });

    return res.status(200).json({ categories, success: true });
  } catch (error) {
    next(error);
  }
};

exports.getsinglecategory = async (req, res, next) => {
  try {
    const { id } = req.params;
    if (!id) {
      return res.status(404).json({ message: "Category id not provided", success: false });
    }

    const category = await Category.findOne({ _id: id }).populate('subcategories');;

    if (!category) {
      return res.status(404).json({ message: "This category Isn't Exists", success: false });
    }

    return res.status(200).json(category);

  } catch (error) {
    next(error);
  }
}

exports.updatecategory = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { categoryName } = req.body;

    const updatecategory = await Category.findByIdAndUpdate(id, { categoryName }, { new: true });

    if (!updatecategory) {
      return res.status(404).json({ message: 'Category not found', success: true });
    }

    return res.status(200).json({ message: 'Category updated successfully', category: updatecategory, success: true });

  } catch (error) {
    next(error);
  }
}

exports.deletecategory = async (req, res, next) => {
  try {
    const { id } = req.params;

    const category = await Category.findById(id);
    if (!category) {
      return res.status(404).json({ message: "Category not Found", success: true });
    }

    // Then call deleteOne() to remove the category itself
    await category.deleteOne();

    return res.status(200).json({ 
      message: "Categorypo deleted Successfully", 
      success: true 
    });
  } catch (error) {
    next(error);
  }
}