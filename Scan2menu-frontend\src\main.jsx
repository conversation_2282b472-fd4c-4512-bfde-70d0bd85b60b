import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'
import { BrowserRouter } from 'react-router-dom'
import UserContext from './context/UserContext.jsx'
import RestaurantContext from './context/RestaurantContext.jsx'
import { GoogleOAuthProvider } from '@react-oauth/google';

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <GoogleOAuthProvider clientId={import.meta.env.VITE_GOOGLE_AUTH_CLIENT_ID}>
      <BrowserRouter>
        <UserContext>
          <RestaurantContext>
            <App />
          </RestaurantContext>
        </UserContext>
      </BrowserRouter>
    </GoogleOAuthProvider>
  </StrictMode>
);
