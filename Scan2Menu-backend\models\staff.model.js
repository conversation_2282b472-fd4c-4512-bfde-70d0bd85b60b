const mongoose = require('mongoose');
const User = require('./user.model');
const Order = require('./order.model');

const staffSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  restaurant_detail: {
    id: { type: String, required: true },
    slug: { type: String, required: true },
  },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'inactive'
  },
  shift: { type: String },
  attendance: [
    {
      date: { type: Date, required: true, default: Date.now },
      presence: {
        type: String,
        enum: ['half day', 'full day', 'leave', 'holiday', 'present'],
        default: 'leave' // Default presence set to 'leave'
      },
      start_time: { type: String, default: null }, // e.g., "09:00 AM"
      end_time: { type: String, default: null }, // e.g., "06:00 PM"
      isClockedIn: { type: Boolean, default: false } // dayin  or not 
    }
  ],
  events: [
    {
      eventName: { type: String },
      date: { type: Date }
    }
  ],
  order_history: [
    {
      orderId: { type: mongoose.Schema.Types.ObjectId, ref: 'Order' },
      date: { type: Date, required: true }
    }
  ],
  working_hours: { type: Number, default: 0 }
}, { timestamps: true });

const Staff = mongoose.model('Staff', staffSchema);
module.exports = Staff;

