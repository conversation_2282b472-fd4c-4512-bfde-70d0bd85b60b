import React, { useState, useRef, useEffect, useContext } from 'react'
import { Outlet, useNavigate } from 'react-router-dom'
import LeftSideBar from '../LeftSideBar/LeftSideBar'
import defaultProfile from '../../assets/default-profile.png'
import { RiNotification3Line, RiSearchLine, RiMenuLine, RiCheckDoubleLine, RiCloseLine, RiRestaurantLine } from 'react-icons/ri'
import Logout from '../../pages/Auth/Logout'
import { getAllTables } from '../../api'
import { RestaurantDataContext } from '../../context/RestaurantContext'
import TableCard from '../TableCard/TableCard'


const Layout = () => {
    const navigate = useNavigate();
    const [showLogout, setShowLogout] = useState(false);
    const [showNotifications, setShowNotifications] = useState(false);
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const notificationRef = useRef(null);
    const [showTables, setShowTables] = useState(false);
    const tablesRef = useRef(null);
    const [tables, setTables] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const { restaurantData } = useContext(RestaurantDataContext);

    // Handle click outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (notificationRef.current && !notificationRef.current.contains(event.target)) {
                setShowNotifications(false);
            }
            if (tablesRef.current && !tablesRef.current.contains(event.target)) {
                setShowTables(false);
            }

        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Sample notifications - replace with your actual data
    const recentNotifications = [
        {
            id: 1,
            type: 'order',
            message: 'New order received for Table 5',
            time: '2 mins ago',
            isRead: false,
            icon: '🛍️'
        },
        {
            id: 2,
            type: 'stock',
            message: 'Menu item "Paneer Tikka" is low on stock',
            time: '10 mins ago',
            isRead: false,
            icon: '📦'
        },
        {
            id: 3,
            type: 'feedback',
            message: 'Customer feedback received',
            time: '30 mins ago',
            isRead: true,
            icon: '💬'
        },
        {
            id: 4,
            type: 'system',
            message: 'Daily revenue report generated',
            time: '1 hour ago',
            isRead: true,
            icon: '📊'
        },
        {
            id: 5,
            type: 'update',
            message: 'System update completed',
            time: '2 hours ago',
            isRead: true,
            icon: '⚙️'
        }
    ];

    const unreadCount = recentNotifications.filter(n => !n.isRead).length;

    const handleMarkAllRead = () => {
        // Add your logic to mark all notifications as read
        console.log('Marked all as read');
    };

    useEffect(() => {
        const fetchTables = async () => {
            try {
                const restaurantSlug = restaurantData?.slug;
                if (!restaurantSlug) {
                    setError('No restaurant Slug found');
                    setLoading(false);
                    return;
                }

                const response = await getAllTables(restaurantSlug);
                if (response.data && response.data.data) {
                    setError(null);
                    setTables(response.data.data);
                } else {
                    setError('Invalid data format received');
                }
            } catch (error) {
                setError(error.message);
            } finally {
                setLoading(false);
            }
        };
        fetchTables();

    }, [restaurantData]);

    return (
        <div className="flex min-h-screen">
            {/* Sidebar - Fixed Position */}
            <aside className={`
                fixed left-0 top-0 h-screen w-64 bg-[#1a1f2e] shadow-lg z-50
                transform transition-transform duration-300
                lg:translate-x-0 ${mobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}
            `}>
                {/* Logo Section */}
                <div className="h-16 flex items-center gap-3 px-6 border-b border-gray-700/50">
                    <svg className="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v1m6 11h2m-6 0h-2m0 0H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent">
                        Scan2Menu
                    </h1>
                </div>

                {/* Sidebar Content */}
                <div className="h-[calc(100vh-4rem)] overflow-y-auto">
                    <LeftSideBar onClose={() => setMobileMenuOpen(false)} />
                </div>
            </aside>

            {/* Main Content Wrapper */}
            <div className="flex-1 lg:ml-64">
                {/* Top Navigation */}
                <header className="fixed top-0 right-0 h-16 left-0 lg:left-64 bg-gradient-to-r from-gray-900 to-gray-800 shadow-lg z-40">
                    <div className="h-full px-4 flex items-center justify-between">
                        {/* Mobile Menu Button */}
                        <button
                            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                            className="lg:hidden p-2 text-white hover:bg-gray-700/50 rounded-lg"
                        >
                            <RiMenuLine className="text-xl" />
                        </button>

                        {/* Search Bar */}
                        <div className="hidden md:flex flex-1 max-w-xl mx-4">
                            <div className="relative w-full">
                                <RiSearchLine className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    className="w-full bg-gray-800/50 border border-gray-700/50 rounded-lg py-2 pl-10 pr-4 text-gray-300 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                                />
                            </div>
                        </div>

                        {/* Right Section */}
                        <div className="flex items-center gap-4">
                            {/* Notifications */}
                            <div className="relative" ref={notificationRef}>
                                <button
                                    onClick={() => setShowNotifications(!showNotifications)}
                                    className="p-2 text-white hover:bg-gray-700/50 rounded-lg"
                                >
                                    <RiNotification3Line className="text-xl" />
                                    {unreadCount > 0 && (
                                        <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full text-xs flex items-center justify-center">
                                            {unreadCount}
                                        </span>
                                    )}
                                </button>

                                {showNotifications && (
                                    <div className="absolute right-0 mt-2 w-80 bg-[#1a1f2e] rounded-lg shadow-xl">
                                        <div className="px-4 py-3 border-b border-gray-700/50">
                                            <div className="flex justify-between items-center mb-2">
                                                <h3 className="text-lg font-semibold text-white">Notifications</h3>
                                                <span className="px-2 py-1 bg-gray-700/50 rounded-full text-xs text-gray-300">
                                                    {unreadCount} new
                                                </span>
                                            </div>
                                            <div className="flex justify-between items-center">
                                                <button
                                                    onClick={handleMarkAllRead}
                                                    className="text-xs text-gray-400 hover:text-gray-300 flex items-center gap-1"
                                                >
                                                    <RiCheckDoubleLine className="text-sm" />
                                                    Mark all as read
                                                </button>
                                                <button
                                                    onClick={() => navigate('/notifications')}
                                                    className="text-xs text-blue-400 hover:text-blue-300"
                                                >
                                                    View All →
                                                </button>
                                            </div>
                                        </div>
                                        <div className="max-h-[400px] overflow-y-auto">
                                            {recentNotifications.map((notification) => (
                                                <div
                                                    key={notification.id}
                                                    className={`px-4 py-3 hover:bg-gray-700/50 cursor-pointer transition-colors duration-200 ${!notification.isRead ? 'bg-gray-800/50' : ''
                                                        }`}
                                                >
                                                    <div className="flex items-start gap-3">
                                                        <span className="text-2xl">{notification.icon}</span>
                                                        <div className="flex-1">
                                                            <p className={`text-sm ${!notification.isRead ? 'text-gray-200 font-medium' : 'text-gray-300'}`}>
                                                                {notification.message}
                                                            </p>
                                                            <p className="text-xs text-gray-500 mt-1">
                                                                {notification.time}
                                                            </p>
                                                        </div>
                                                        {!notification.isRead && (
                                                            <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                                                        )}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Tables Status */}
                            <div className="relative" ref={tablesRef}>
                                <button
                                    onClick={() => {
                                        setShowTables(!showTables);
                                    }}
                                    className="p-2 text-white hover:bg-gray-700/50 rounded-lg relative"
                                >
                                    <RiRestaurantLine className="text-xl" />
                                    {!loading && tables.filter(t => t.status === 'occupied').length > 0 && (
                                        <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full text-xs flex items-center justify-center">
                                            {tables.filter(t => t.status === 'occupied').length}
                                        </span>
                                    )}
                                </button>

                                {showTables && (
                                    <div className="absolute right-0 mt-2 w-80 bg-[#1a1f2e] rounded-lg shadow-xl">
                                        <div className="px-4 py-3 border-b border-gray-700/50">
                                            <div className="flex justify-between items-center">
                                                <h3 className="text-lg font-semibold text-white">Table Status</h3>
                                                <span className="px-2 py-1 bg-gray-700/50 rounded-full text-xs text-gray-300">
                                                    {tables.filter(t => t.status === 'occupied').length} occupied
                                                </span>
                                            </div>
                                        </div>

                                        <div className="p-4 grid grid-cols-3 gap-3 max-h-[400px] overflow-y-auto">
                                            {loading ? (
                                                <div className="col-span-3 text-center text-gray-400">Loading...</div>
                                            ) : error ? (
                                                <div className="col-span-3 text-center text-red-400">{error}</div>
                                            ) : tables.length === 0 ? (
                                                <div className="col-span-3 text-center text-gray-400">No tables found</div>
                                            ) : (
                                                tables.map((table) => (
                                                    <TableCard
                                                        key={table._id}
                                                        table={table}
                                                        onViewOrder={(orderId) => {
                                                            setShowTables(false);
                                                            navigate(`/order/${orderId}`);
                                                        }}
                                                    />
                                                ))
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Profile */}
                            <div className="relative">
                                <button
                                    onClick={() => setShowLogout(!showLogout)}
                                    className="flex items-center gap-2 p-1.5 text-white hover:bg-gray-700/50 rounded-lg"
                                >
                                    <img src={defaultProfile} className="w-8 h-8 rounded-full" alt="Profile" />
                                    <span className="hidden md:block">{JSON.parse(localStorage.getItem('user'))?.name}</span>
                                </button>

                                {showLogout && (
                                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl">
                                        <Logout />
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </header>

                {/* Main Content */}
                <main className="pt-16">
                    <div className="p-4 md:p-6 lg:p-8">
                        <Outlet />

                    </div>
                </main>
            </div>

            {/* Mobile Menu Overlay */}
            {mobileMenuOpen && (
                <div
                    className="fixed inset-0 bg-black/50 z-30 lg:hidden"
                    onClick={() => setMobileMenuOpen(false)}
                />
            )}
        </div>
    );
};

export default Layout