const jwt = require('jsonwebtoken');
const User = require('../models/user.model')
const blackListTokenModel = require('../models/blacklistToken.model');


exports.authMiddeleware = async (req, res, next) => {
    try {
        const token = req.headers.authorization?.split(' ')[1];
        if (!token) {
            return res.status(401).json({ message: 'Unauthorized access', success: false });

        }
        const isBlacklisted = await blackListTokenModel.findOne({ token });
        if (isBlacklisted) {
            return res.status(401).json({ message: 'Unauthorized' });
        }
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.id);
        if (!user) {
            return res.status(404).json({ message: 'Unauthorized access', success: false });
        }
        req.user = user;
        next();
    } catch (error) {
        console.log(error)
        next(error)

    }
}