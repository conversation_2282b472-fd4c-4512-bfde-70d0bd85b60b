const express = require('express');
const   router = express.Router();
const itemController = require('../controllers/item.controller');
const { upload } = require('../config/multer.config'); // Import Cloudinary configuration

// Create new item with image upload
router.post('/',upload.single('subimage'),itemController.createItem);

// Route to add extra to an item
router.put('/add-extra/:id', itemController.addExtraToItem);

// Route to remove extra from an item
router.put('/remove-extra/:id', itemController.removeExtraFromItem);

// Reorder items for drag and drop functionality
router.put('/reorder', itemController.reorderItems);

// Update existing item with optional image upload
router.put('/:id',upload.single('subimage'),itemController.updateItem);

// Get all items from database
router.get('/', itemController.getAllItems);

// Get a single item by its ID
router.get('/:id', itemController.getItemById);

// Delete an item and remove its references from category/subcategory
router.delete('/:id', itemController.deleteItem);


module.exports = router;
