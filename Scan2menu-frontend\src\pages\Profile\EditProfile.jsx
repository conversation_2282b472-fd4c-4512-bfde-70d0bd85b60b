import React, { useState, useEffect } from 'react';
import { updateUser } from '../../api';
import { toast } from 'react-hot-toast';

const EditProfile = () => {
  const [profileData, setProfileData] = useState({
    name: '',
    username: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: ''
  });

  useEffect(() => {
    const userData = JSON.parse(localStorage.getItem('user'));
    if (userData) {
      setProfileData(prev => ({
        ...prev,
        name: userData.name || '',
        username: userData.username || '',
        email: userData.email || '',
        phone: userData.phone || '',
      }));
    }
  }, []);

  const handleInputChange = (key, value) => {
    setProfileData(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const validatePassword = (password) => {
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(password);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation
    if (!profileData.name || !profileData.email || !profileData.phone) {
      toast.error('Please fill all required fields');
      return;
    }

    // Only validate passwords if either password field is filled
    if (profileData.password || profileData.confirmPassword) {
      if (!profileData.password || !profileData.confirmPassword) {
        toast.error('Please fill both password fields');
        return;
      }
      if (profileData.password !== profileData.confirmPassword) {
        toast.error('Passwords do not match');
        return;
      }
      if (!validatePassword(profileData.password)) {
        toast.error('Password must contain at least 8 characters, including 1 lowercase letter, 1 uppercase letter, 1 number, and 1 special character');
        return;
      }
    }

    try {
      const userData = JSON.parse(localStorage.getItem('user'));
      const updateData = {
        name: profileData.name,
        email: profileData.email,
        phone: profileData.phone
      };

      // Only include password in update if it was provided and validated
      if (profileData.password) {
        updateData.password = profileData.password;
      }

      const response = await updateUser(userData._id, updateData);
      if (response.data.success) {
        // Remove password from userData before storing
        const updatedUserData = {
          ...userData,
          name: updateData.name,
          email: updateData.email,
          phone: updateData.phone
        };

        localStorage.setItem('user', JSON.stringify(updatedUserData));

        // Clear password fields after successful update
        setProfileData(prev => ({
          ...prev,
          password: '',
          confirmPassword: ''
        }));

        toast.success(profileData.password
          ? 'Profile and password updated successfully'
          : 'Profile updated successfully'
        );
      }
    } catch (error) {
      toast.error(error?.response?.data?.message || 'Error updating profile');
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">Edit Profile</h1>
          <p className="text-gray-600">Update your profile information</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6">Personal Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Name</label>
                <input
                  type="text"
                  value={profileData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Username</label>
                <input
                  type="text"
                  value={profileData.username}
                  disabled
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100 cursor-not-allowed"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Email</label>
                <input
                  type="email"
                  value={profileData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Phone</label>
                <input
                  type="tel"
                  value={profileData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">New Password</label>
                <input
                  type="password"
                  value={profileData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Leave blank to keep current password"
                />
                <p className="text-xs text-gray-500">
                  Password must contain at least 8 characters, including 1 lowercase letter,
                  1 uppercase letter, 1 number, and 1 special character
                </p>
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Confirm Password</label>
                <input
                  type="password"
                  value={profileData.confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Leave blank to keep current password"
                />
              </div>
            </div>
          </div>

          <button
            type="submit"
            className="w-full sm:w-auto mx-auto bg-gradient-to-r from-green-400 to-green-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-green-500 hover:to-green-700 transition duration-300 shadow-md"
          >
            Save Changes
          </button>
        </form>
      </div>
    </div>
  );
};

export default EditProfile;