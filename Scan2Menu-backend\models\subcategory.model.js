const mongoose = require('mongoose');
const Category = require('./category.model');
const Item = require('./item.model')

const subcategorySchema = new mongoose.Schema(
  {
    // Reference to the parent Category
    categoryId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Category', // Refers to the Category model
      required: true,
    },
    // Name of the subcategory
    subcategoryName: {
      type: String,
      required: true,
    },
    // Array of items linked to this subcategory
    items: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Item', // Refers to the Item model
      },
    ],
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt fields
  }
);

// Middleware to handle cascade delete
subcategorySchema.pre('remove', async function (next) {
  try {
    // Delete all items linked to this category
    await Item.deleteMany({ categoryId: this._id });
    next();
  } catch (err) {
    next(err);
  }
});

const Subcategory = mongoose.model('Subcategory', subcategorySchema);

module.exports = Subcategory;
