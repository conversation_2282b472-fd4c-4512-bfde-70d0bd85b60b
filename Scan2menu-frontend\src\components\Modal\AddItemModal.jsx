import React, { useState, useEffect } from 'react';
import { RiCloseLine, RiUploadLine } from 'react-icons/ri';
import { Button } from '../ui/button';
import InputField from '../InputFieldValidation/InputField';
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from '../ui/select';
import { Loader } from 'lucide-react';
const AddItemModal = ({ loading, setLoading, isOpen, onClose, onSave, categoryId, subcategoryId = null, editItem = null }) => {

  const initialFormState = {
    title: '',
    detail: '',
    price: '',
    type: '',
    subimage: null, // File object
    imagePreview: '', // Preview URL
    menuButton: true
  };

  const [formData, setFormData] = useState(initialFormState);
  const [error, setError] = useState({});

  // Reset form when modal opens or editItem changes
  useEffect(() => {
    if (isOpen) {
      if (editItem) {
        setFormData({
          title: editItem.title || '',
          detail: editItem.detail || '',
          price: editItem.price || '',
          type: editItem.type || '',
          subimage: null, // Reset file for editing
          imagePreview: editItem.image || '', // Use the existing image URL
          menuButton: editItem.menuButton ?? true
        });
      } else {
        setFormData(initialFormState);
      }
      setError({});
    }
  }, [isOpen, editItem]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
    setError((prev) => ({ ...prev, [name]: '' }));
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData((prev) => ({
        ...prev,
        subimage: file, // Store the file object
        imagePreview: URL.createObjectURL(file) // Create a preview URL
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const errors = {};
    if (!formData.title.trim()) errors.title = 'Title is required';
    if (!formData?.price) errors.price = 'Price is required';
    if (!formData.type) errors.type = 'Type is required';

    if (Object.keys(errors).length > 0) {
      setError(errors);
      return;
    }

    // Construct the payload
    const payload = new FormData();
    payload.append('title', formData.title);
    payload.append('detail', formData.detail);
    payload.append('price', formData.price);
    payload.append('type', formData.type);
    payload.append('menuButton', formData.menuButton);
    if (categoryId) {
      payload.append('categoryId', categoryId);
    };
    if (subcategoryId) {
      payload.append('subcategoryId', subcategoryId);
    };
    if (formData.subimage) {
      payload.append('subimage', formData.subimage); // Append file object
    }
    await onSave(payload);

  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden">
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose} />

      <div className="relative bg-white rounded-xl shadow-xl w-full max-w-md mx-4 my-6">
        {/* Sticky Header */}
        <div className="sticky top-0 bg-white px-6 py-4 border-b border-gray-200 rounded-t-xl z-10">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-800">
              {editItem ? 'Edit Item' : 'Add New Item'} to {categoryId ? 'Category' : 'Subcategory'}
            </h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <RiCloseLine className="text-xl text-gray-500" />
            </button>
          </div>
        </div>

        {/* Scrollable Content */}
        <div className="px-6 py-4 max-h-[calc(100vh-12rem)] overflow-y-auto">
          <form onSubmit={handleSubmit} className="space-y-6">
            <InputField
              label="Item Title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              error={error.title}
              placeholder="Enter item title"
            />

            <InputField
              label="Item Detail"
              name="detail"
              value={formData.detail}
              onChange={handleChange}
              error={error.detail}
              placeholder="Enter item description"
              multiline
              rows={3}
            />

            <InputField
              label="Price"
              name="price"
              type="number"
              value={formData.price}
              onChange={handleChange}
              error={error.price}
              placeholder="Enter price"
            />

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Type</label>
              <Select
                value={formData.type}
                onValueChange={(value) => {
                  setFormData((prev) => ({ ...prev, type: value }))
                  setError((prev) => ({ ...prev, type: '' }))
                }}
              >
                <SelectTrigger className={`w-full ${error.type ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : 'border-gray-200'}`}>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="veg">Veg</SelectItem>
                  <SelectItem value="non-veg">Non-Veg</SelectItem>
                </SelectContent>
              </Select>
              {error.type && <p className="text-sm text-red-500">{error.type}</p>}
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Item Image</label>
              <div className="border-2 border-dashed border-gray-200 rounded-lg p-4">
                {formData.imagePreview ? (
                  <div className="relative">
                    <img
                      src={formData.imagePreview}
                      alt="Preview"
                      className="w-full h-48 object-cover rounded-lg"
                    />
                    <button
                      type="button"
                      onClick={() =>
                        setFormData((prev) => ({ ...prev, subimage: null, imagePreview: '' }))
                      }
                      className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                    >
                      <RiCloseLine className="text-lg" />
                    </button>
                  </div>
                ) : (
                  <label className="flex flex-col items-center justify-center h-48 cursor-pointer hover:bg-gray-50 transition-colors">
                    <RiUploadLine className="text-4xl text-gray-400" />
                    <span className="mt-2 text-sm text-gray-500">Upload Image</span>
                    <input
                      type="file"
                      className="hidden"
                      accept='image/jpeg, image/png, image/jpg'
                      onChange={handleImageUpload}
                    />
                  </label>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                name="menuButton"
                checked={formData.menuButton}
                onChange={handleChange}
                className="w-4 h-4 text-[#22c55e] border-gray-300 rounded focus:ring-[#22c55e]"
              />
              <label className="text-sm text-gray-700">Available</label>
            </div>
          </form>
        </div>

        {/* Sticky Footer */}
        <div className="sticky bottom-0 bg-white px-6 py-4 border-t border-gray-200 rounded-b-xl">
          <Button
            type="submit"
            onClick={handleSubmit}
            className="w-full bg-gradient-to-r from-[#E28E66] to-[#F2A44F] hover:from-[#d68660] hover:to-[#e69847] text-white py-2 rounded-lg transition-colors"
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader className="h-4 w-4 animate-spin mr-2" />
                Please wait
              </>
            ) : (
              editItem ? 'Update Item' : 'Save Item'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AddItemModal;
