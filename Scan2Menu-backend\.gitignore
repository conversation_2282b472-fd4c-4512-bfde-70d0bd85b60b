# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.vscode/

# Logs
logs/
*.log
logs/*.log
pids/
*.pid
*.seed
*.pid.lock

# Dependency directories
node_modules/
bower_components/

# Build output
dist/
build/
.out/
.cache/
coverage/
*.tsbuildinfo

# Environment files
.env
.env.local
.env.development
.env.test
.env.production
.env.*.local

# Database files
*.sqlite
*.sqlite3
db.json
local_db/
*.sql
*.db

# Generated keys and certificates
*.pem
*.cert
*.key

# Testing
coverage/
.nyc_output/
jest/
cypress/screenshots/
cypress/videos/
cypress/coverage/
*.lcov

# TypeScript
*.tsbuildinfo

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS files
.DS_Store
Thumbs.db
desktop.ini

# Lint & Formatting
.eslintcache
.prettierignore
.prettiercache
stylelintcache

# Optional: Lock files (comment out if you want to track them)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# PM2 (Process Manager)
pids/
*.pid
*.pid.lock
.pm2/

# Docker
docker-compose.override.yml
.dockerignore
Dockerfile*
.docker/

# AWS / Azure / GCP Secrets
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Miscellaneous
*.bak
*.orig
*.rej
*.gz
*.tgz
*.tar
*.zip

# Local development
.vscode/
.history/
.DS_Store
