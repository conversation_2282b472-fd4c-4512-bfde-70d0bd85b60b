{"name": "scan2menu-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^6.3.0", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@react-oauth/google": "^0.12.1", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.469.0", "qrcode.react": "^4.2.0", "razorpay": "^2.9.6", "react": "^18.3.1", "react-big-calendar": "^1.18.0", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.1", "react-icons": "^5.4.0", "react-qr-code": "^2.0.15", "react-router-dom": "^7.1.1", "tailwind-merge": "^2.6.0", "tailwind-scrollbar": "^3.1.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "vite": "^6.0.5"}}