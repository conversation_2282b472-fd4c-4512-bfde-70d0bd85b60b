const mongoose = require('mongoose');

const qrCodeSchema = new mongoose.Schema({
  restaurantId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Restaurant',
    required: true,
    unique: true
  },
  qrCodeSettings: {
    foregroundColor: {
      type: String,
      default: '#000000'
    },
    backgroundColor: {
      type: String,
      default: '#FFFFFF'
    },
    padding: {
      type: Number,
      default: 5
    },
    cornerRadius: {
      type: Number,
      default: 50
    },
    displayText: {
      type: String,
      default: ''
    },
    textColor: {
      type: String,
      default: '#00FF00'
    },
    textSize: {
      type: Number,
      default: 16
    }
  }
}, { timestamps: true });

const QRCode = mongoose.model('QRCode', qrCodeSchema);
module.exports = QRCode;
