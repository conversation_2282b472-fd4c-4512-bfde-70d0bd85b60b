"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e,t=require("react"),r=(e=t)&&"object"==typeof e&&"default"in e?e.default:e,n=require("@dnd-kit/core"),o=require("@dnd-kit/utilities");function i(e,t,r){const n=e.slice();return n.splice(r<0?n.length+r:r,0,n.splice(t,1)[0]),n}function a(e,t){return e.reduce((e,r,n)=>{const o=t.get(r);return o&&(e[n]=o),e},Array(e.length))}function s(e){return null!==e&&e>=0}const d={scaleX:1,scaleY:1},l=e=>{let{rects:t,activeIndex:r,overIndex:n,index:o}=e;const a=i(t,n,r),s=t[o],d=a[o];return d&&s?{x:d.left-s.left,y:d.top-s.top,scaleX:d.width/s.width,scaleY:d.height/s.height}:null},c={scaleX:1,scaleY:1},u=r.createContext({activeIndex:-1,containerId:"Sortable",disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:l,disabled:{draggable:!1,droppable:!1}}),f=e=>{let{id:t,items:r,activeIndex:n,overIndex:o}=e;return i(r,n,o).indexOf(t)},p=e=>{let{containerId:t,isSorting:r,wasDragging:n,index:o,items:i,newIndex:a,previousItems:s,previousContainerId:d,transition:l}=e;return!(!l||!n||s!==i&&o===a||!r&&(a===o||t!==d))},g={duration:200,easing:"ease"},b=o.CSS.Transition.toString({property:"transform",duration:0,easing:"linear"}),x={roleDescription:"sortable"};function v(e){if(!e)return!1;const t=e.data.current;return!!(t&&"sortable"in t&&"object"==typeof t.sortable&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable)}const h=[n.KeyboardCode.Down,n.KeyboardCode.Right,n.KeyboardCode.Up,n.KeyboardCode.Left];function I(e,t){return!(!v(e)||!v(t))&&e.data.current.sortable.containerId===t.data.current.sortable.containerId}exports.SortableContext=function(e){let{children:i,id:s,items:d,strategy:c=l,disabled:f=!1}=e;const{active:p,dragOverlay:g,droppableRects:b,over:x,measureDroppableContainers:v}=n.useDndContext(),h=o.useUniqueId("Sortable",s),I=Boolean(null!==g.rect),y=t.useMemo(()=>d.map(e=>"object"==typeof e&&"id"in e?e.id:e),[d]),m=null!=p,w=p?y.indexOf(p.id):-1,C=x?y.indexOf(x.id):-1,R=t.useRef(y),S=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}(y,R.current),D=-1!==C&&-1===w||S,O=function(e){return"boolean"==typeof e?{draggable:e,droppable:e}:e}(f);o.useIsomorphicLayoutEffect(()=>{S&&m&&v(y)},[S,y,m,v]),t.useEffect(()=>{R.current=y},[y]);const N=t.useMemo(()=>({activeIndex:w,containerId:h,disabled:O,disableTransforms:D,items:y,overIndex:C,useDragOverlay:I,sortedRects:a(y,b),strategy:c}),[w,h,O.draggable,O.droppable,D,y,C,b,I,c]);return r.createElement(u.Provider,{value:N},i)},exports.arrayMove=i,exports.arraySwap=function(e,t,r){const n=e.slice();return n[t]=e[r],n[r]=e[t],n},exports.defaultAnimateLayoutChanges=p,exports.defaultNewIndexGetter=f,exports.hasSortableData=v,exports.horizontalListSortingStrategy=e=>{var t;let{rects:r,activeNodeRect:n,activeIndex:o,overIndex:i,index:a}=e;const s=null!=(t=r[o])?t:n;if(!s)return null;const l=function(e,t,r){const n=e[t],o=e[t-1],i=e[t+1];return n&&(o||i)?r<t?o?n.left-(o.left+o.width):i.left-(n.left+n.width):i?i.left-(n.left+n.width):n.left-(o.left+o.width):0}(r,a,o);if(a===o){const e=r[i];return e?{x:o<i?e.left+e.width-(s.left+s.width):e.left-s.left,y:0,...d}:null}return a>o&&a<=i?{x:-s.width-l,y:0,...d}:a<o&&a>=i?{x:s.width+l,y:0,...d}:{x:0,y:0,...d}},exports.rectSortingStrategy=l,exports.rectSwappingStrategy=e=>{let t,r,{activeIndex:n,index:o,rects:i,overIndex:a}=e;return o===n&&(t=i[o],r=i[a]),o===a&&(t=i[o],r=i[n]),r&&t?{x:r.left-t.left,y:r.top-t.top,scaleX:r.width/t.width,scaleY:r.height/t.height}:null},exports.sortableKeyboardCoordinates=(e,t)=>{let{context:{active:r,collisionRect:i,droppableRects:a,droppableContainers:s,over:d,scrollableAncestors:l}}=t;if(h.includes(e.code)){if(e.preventDefault(),!r||!i)return;const t=[];s.getEnabled().forEach(r=>{if(!r||null!=r&&r.disabled)return;const o=a.get(r.id);if(o)switch(e.code){case n.KeyboardCode.Down:i.top<o.top&&t.push(r);break;case n.KeyboardCode.Up:i.top>o.top&&t.push(r);break;case n.KeyboardCode.Left:i.left>o.left&&t.push(r);break;case n.KeyboardCode.Right:i.left<o.left&&t.push(r)}});const f=n.closestCorners({active:r,collisionRect:i,droppableRects:a,droppableContainers:t,pointerCoordinates:null});let p=n.getFirstCollision(f,"id");if(p===(null==d?void 0:d.id)&&f.length>1&&(p=f[1].id),null!=p){const e=s.get(r.id),t=s.get(p),d=t?a.get(t.id):null,f=null==t?void 0:t.node.current;if(f&&d&&e&&t){const r=n.getScrollableAncestors(f).some((e,t)=>l[t]!==e),a=I(e,t),s=(u=t,!(!v(c=e)||!v(u))&&!!I(c,u)&&c.data.current.sortable.index<u.data.current.sortable.index),p=r||!a?{x:0,y:0}:{x:s?i.width-d.width:0,y:s?i.height-d.height:0},g={x:d.left,y:d.top};return p.x&&p.y?g:o.subtract(g,p)}}}var c,u},exports.useSortable=function(e){let{animateLayoutChanges:r=p,attributes:i,disabled:a,data:d,getNewIndex:l=f,id:c,strategy:v,resizeObserverConfig:h,transition:I=g}=e;const{items:y,containerId:m,activeIndex:w,disabled:C,disableTransforms:R,sortedRects:S,overIndex:D,useDragOverlay:O,strategy:N}=t.useContext(u),E=function(e,t){var r,n;return"boolean"==typeof e?{draggable:e,droppable:!1}:{draggable:null!=(r=null==e?void 0:e.draggable)?r:t.draggable,droppable:null!=(n=null==e?void 0:e.droppable)?n:t.droppable}}(a,C),K=y.indexOf(c),L=t.useMemo(()=>({sortable:{containerId:m,index:K,items:y},...d}),[m,d,K,y]),T=t.useMemo(()=>y.slice(y.indexOf(c)),[y,c]),{rect:M,node:A,isOver:k,setNodeRef:X}=n.useDroppable({id:c,data:L,disabled:E.droppable,resizeObserverConfig:{updateMeasurementsFor:T,...h}}),{active:Y,activatorEvent:j,activeNodeRect:q,attributes:z,setNodeRef:U,listeners:B,isDragging:F,over:P,setActivatorNodeRef:_,transform:G}=n.useDraggable({id:c,data:L,attributes:{...x,...i},disabled:E.draggable}),H=o.useCombinedRefs(X,U),J=Boolean(Y),Q=J&&!R&&s(w)&&s(D),V=!O&&F,W=V&&Q?G:null,Z=Q?null!=W?W:(null!=v?v:N)({rects:S,activeNodeRect:q,activeIndex:w,overIndex:D,index:K}):null,$=s(w)&&s(D)?l({id:c,items:y,activeIndex:w,overIndex:D}):K,ee=null==Y?void 0:Y.id,te=t.useRef({activeId:ee,items:y,newIndex:$,containerId:m}),re=y!==te.current.items,ne=r({active:Y,containerId:m,isDragging:F,isSorting:J,id:c,index:K,items:y,newIndex:te.current.newIndex,previousItems:te.current.items,previousContainerId:te.current.containerId,transition:I,wasDragging:null!=te.current.activeId}),oe=function(e){let{disabled:r,index:i,node:a,rect:s}=e;const[d,l]=t.useState(null),c=t.useRef(i);return o.useIsomorphicLayoutEffect(()=>{if(!r&&i!==c.current&&a.current){const e=s.current;if(e){const t=n.getClientRect(a.current,{ignoreTransform:!0}),r={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(r.x||r.y)&&l(r)}}i!==c.current&&(c.current=i)},[r,i,a,s]),t.useEffect(()=>{d&&l(null)},[d]),d}({disabled:!ne,index:K,node:A,rect:M});return t.useEffect(()=>{J&&te.current.newIndex!==$&&(te.current.newIndex=$),m!==te.current.containerId&&(te.current.containerId=m),y!==te.current.items&&(te.current.items=y)},[J,$,m,y]),t.useEffect(()=>{if(ee===te.current.activeId)return;if(null!=ee&&null==te.current.activeId)return void(te.current.activeId=ee);const e=setTimeout(()=>{te.current.activeId=ee},50);return()=>clearTimeout(e)},[ee]),{active:Y,activeIndex:w,attributes:z,data:L,rect:M,index:K,newIndex:$,items:y,isOver:k,isSorting:J,isDragging:F,listeners:B,node:A,overIndex:D,over:P,setNodeRef:H,setActivatorNodeRef:_,setDroppableNodeRef:X,setDraggableNodeRef:U,transform:null!=oe?oe:Z,transition:oe||re&&te.current.newIndex===K?b:V&&!o.isKeyboardEvent(j)||!I?void 0:J||ne?o.CSS.Transition.toString({...I,property:"transform"}):void 0}},exports.verticalListSortingStrategy=e=>{var t;let{activeIndex:r,activeNodeRect:n,index:o,rects:i,overIndex:a}=e;const s=null!=(t=i[r])?t:n;if(!s)return null;if(o===r){const e=i[a];return e?{x:0,y:r<a?e.top+e.height-(s.top+s.height):e.top-s.top,...c}:null}const d=function(e,t,r){const n=e[t],o=e[t-1],i=e[t+1];return n?r<t?o?n.top-(o.top+o.height):i?i.top-(n.top+n.height):0:i?i.top-(n.top+n.height):o?n.top-(o.top+o.height):0:0}(i,o,r);return o>r&&o<=a?{x:0,y:-s.height-d,...c}:o<r&&o>=a?{x:0,y:s.height+d,...c}:{x:0,y:0,...c}};
//# sourceMappingURL=sortable.cjs.production.min.js.map
