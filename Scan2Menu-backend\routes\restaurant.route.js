const express = require('express');
const router = express.Router();
const restaurantController = require('../controllers/restaurant.controller');
const { upload } = require('../config/multer.config'); // Import Cloudinary configuration

// Use Multer middleware for handling image uploads
router.post(
  '/',
  upload.fields([
    { name: 'image', maxCount: 1 }, // Single restaurant image
    { name: 'coverImage', maxCount: 1 }, // Single cover image
  ]),
  restaurantController.createRestaurant
);

router.put(
  '/:id',
  upload.fields([
    { name: 'image', maxCount: 1 }, // Single restaurant image
    { name: 'coverImage', maxCount: 1 }, // Single cover image
  ]),
  restaurantController.updateRestaurant
);

router.get('/', restaurantController.getAllRestaurants);
router.get('/dashboard/:id', restaurantController.dashboard)
router.get('/:param', restaurantController.getRestaurant);
router.delete('/:param', restaurantController.deleteRestaurant);

module.exports = router;
