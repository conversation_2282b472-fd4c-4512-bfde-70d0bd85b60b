import {
  require_react
} from "./chunk-RLJ2RCJQ.js";
import {
  __toESM
} from "./chunk-DC5AMYBS.js";

// node_modules/@mui/material/Dialog/DialogContext.js
var React = __toESM(require_react());
var DialogContext = React.createContext({});
if (true) {
  DialogContext.displayName = "DialogContext";
}
var DialogContext_default = DialogContext;

export {
  DialogContext_default
};
//# sourceMappingURL=chunk-BMKNEMPU.js.map
