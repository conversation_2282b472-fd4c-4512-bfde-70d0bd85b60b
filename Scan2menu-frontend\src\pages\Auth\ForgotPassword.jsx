// import { useState } from "react";
// import axios from "axios";

// const ForgotPassword = () => {
//   const [email, setEmail] = useState("");
//   const [message, setMessage] = useState("");
//   const [error, setError] = useState("");
//   const [loading, setLoading] = useState(false);

//   const handleSubmit = async (e) => {
//     e.preventDefault();
//     setMessage("");
//     setError("");
//     setLoading(true);

//     try {
//       const baseurl = import.meta.env.VITE_APP_API_URL;
//       const response = await axios.post(
//         `${baseurl}/users/forgot-password`,
//         { email }
//       );
//       setMessage(response.data.message);
//     } catch (err) {
//       setError(err.response?.data?.message || "Something went wrong.");
//     } finally {
//       setLoading(false);
//     }
//   };

//   return (
//     <div className="flex items-center justify-center h-screen bg-gray-100">
//       <div className="w-full max-w-md bg-white p-6 rounded-lg shadow-md">
//         <h2 className="text-2xl font-bold text-center mb-4">Forgot Password</h2>

//         {message && <p className="text-green-500 text-center">{message}</p>}
//         {error && <p className="text-red-500 text-center">{error}</p>}

//         <form onSubmit={handleSubmit} className="space-y-4">
//           <div>
//             <label className="block text-gray-700 font-medium">Email Address</label>
//             <input
//               type="email"
//               className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
//               placeholder="Enter your email"
//               value={email}
//               onChange={(e) => setEmail(e.target.value)}
//               required
//             />
//           </div>

//           <button
//             type="submit"
//             className="w-full bg-blue-500 text-white py-2 rounded-lg font-semibold hover:bg-blue-600 transition"
//             disabled={loading}
//           >
//             {loading ? "Sending..." : "Send Reset Link"}
//           </button>
//         </form>
//       </div>
//     </div>
//   );
// };

// export default ForgotPassword;

import React, { useState } from 'react';
import axios from 'axios';
import { toast } from 'react-hot-toast';
import { useNavigate, Link } from 'react-router-dom';
import { Loader2 } from 'lucide-react';
import InputField from '../../components/InputFieldValidation/InputField';
import { Button } from '../../components/ui/button';

const ForgotPassword = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [formData, setFormData] = useState({
    email: '',
  });

  const validateForm = () => {
    const newErrors = {};

    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // const handleSubmit = async (e) => {
  //   e.preventDefault();

  //   if (validateForm()) {
  //     setLoading(true);
  //     try {
  //       const baseurl = import.meta.env.VITE_APP_LOCAL_API_URL;
  //       const response = await axios.post(
  //         `${baseurl}/users/forgot-password`,
  //         { email: formData.email }
  //       );
        
  //       toast.success(response.data.message || 'Password reset link sent to your email');
  //       navigate('/login');
  //     } catch (error) {
  //       toast.error(error.response?.data?.message || 'Something went wrong, please try again');
  //     } finally {
  //       setLoading(false);
  //     }
  //   }
  // };


  const handleSubmit = async (e) => {
    e.preventDefault();
  
    if (validateForm()) {
      setLoading(true);
      try {
        const baseurl = import.meta.env.VITE_APP_API_URL
        const response = await axios.post(
          `${baseurl}/users/forgot-password`,
          { email: formData.email }
        );
  
        console.log("API Response:", response); // Debugging
        if (response.data && response.data.success) {
          toast.success(response.data.message || 'Password reset link sent to your email');
          setTimeout(() => navigate('/login'), 3000); // Delay navigation for user feedback
        } else {
          toast.error('Unexpected response from server');
        }
      } catch (error) {
        console.error("API Error:", error.response?.data || error); // Debugging
        toast.error(error.response?.data?.message || 'Something went wrong, please try again');
      } finally {
        setLoading(false);
      }
    }
  };
  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    if (errors[e.target.name]) {
      setErrors({
        ...errors,
        [e.target.name]: '',
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FFF5E6] to-[#FFE5CC] py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="bg-white rounded-3xl shadow-2xl overflow-hidden">
          <div className="px-6 py-8 text-center">
            <div className="flex items-center justify-center gap-2 mb-6">
              <h1 className="text-3xl font-bold text-[#EFA052]">Scan2Menu</h1>
            </div>
            <h2 className="text-2xl font-semibold text-gray-800">Forgot Password</h2>
            <p className="mt-2 text-gray-600">Enter your email to receive a reset link</p>
          </div>

          <form onSubmit={handleSubmit} className="px-6 pb-8">
            <div className="space-y-4">
              <InputField
                label="Email Address"
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                error={errors.email}
                placeholder="Enter your email"
                className="rounded-xl border-gray-300 focus:border-[#EFA052] focus:ring focus:ring-[#EFA052] focus:ring-opacity-50"
              />

              <Button
                type="submit"
                className="w-full py-3 font-medium text-white bg-[#EFA052] hover:bg-[#D88A3B] rounded-xl transition-colors"
                disabled={loading}
              >
                {loading ? (
                  <div className="flex items-center justify-center gap-2">
                    <Loader2 className="animate-spin" size={20} />
                    <span>Sending...</span>
                  </div>
                ) : (
                  'Send Reset Link'
                )}
              </Button>
            </div>

            <p className="mt-6 text-center text-sm text-gray-600">
              Remember your password?{' '}
              <Link to="/login" className="text-[#EFA052] hover:text-[#D88A3B] font-semibold">
                Login
              </Link>
            </p>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;