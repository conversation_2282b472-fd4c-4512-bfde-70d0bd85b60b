const Joi = require('joi');

// Validation schema for Subcategory
const subcategorySchema = Joi.object({
  categoryId: Joi.string().required().label('Category ID'),
  subcategoryName: Joi.string().min(3).max(50).required().label('Subcategory Name'),
  items: Joi.array()
    .items(
      Joi.object({
        title: Joi.string().required().label('Item Title'),
        price: Joi.number().required().label('Item Price'),
        type: Joi.string().valid('veg', 'non-veg').required().label('Item Type'),
      })
    )
    .optional()
    .label('Items'),
});

module.exports = { subcategorySchema };
