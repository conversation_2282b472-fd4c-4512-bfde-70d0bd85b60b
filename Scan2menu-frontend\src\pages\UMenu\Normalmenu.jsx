import React, { useEffect, useState } from "react";
import { getwholeMenu } from "../../api";
import { useParams } from "react-router-dom";
import { toast } from "react-hot-toast";
import { Search, Loader2 } from "lucide-react";

const Normalmenu = () => {
  const { slug } = useParams();
  const [menuResponse, setMenuResponse] = useState(null);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [activeCategory, setActiveCategory] = useState("all");

  useEffect(() => {
    async function fetchMenu() {
      setLoading(true);
      try {
        const response = await getwholeMenu(slug);
        if (response.data.success) {
          setMenuResponse(response.data);
        } else {
          toast.error("Failed to load menu");
        }
      } catch (error) {
        toast.error(error.response?.data?.message || error.message);
      } finally {
        setLoading(false);
      }
    }
    fetchMenu();
  }, [slug]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-50">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (!menuResponse) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-50">
        <p className="text-lg text-gray-600">No menu found.</p>
      </div>
    );
  }

  const { restaurant, data: categories } = menuResponse;

  const filteredCategories = categories
    .filter((category) => activeCategory === "all" || category._id === activeCategory)
    .map((category) => ({
      ...category,
      items: category.items.filter(
        (item) =>
          (filterType === "all" || item.type === filterType) &&
          (item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            item.detail.toLowerCase().includes(searchQuery.toLowerCase()))
      ),
      subcategories: category.subcategories?.map((subcategory) => ({
        ...subcategory,
        items: subcategory.items.filter(
          (item) =>
            (filterType === "all" || item.type === filterType) &&
            (item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
              item.detail.toLowerCase().includes(searchQuery.toLowerCase()))
        ),
      })),
    }))
    .filter((category) => category.items.length > 0 || category.subcategories?.some((sub) => sub.items.length > 0));

  return (
    <div className="min-h-screen bg-gray-50 pb-10">
      {/* Restaurant Header */}
      <div className="relative h-44 sm:h-52">
        <img src={restaurant.image} alt={restaurant.name} className="w-full h-full object-cover" />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-black/30 flex items-end p-4">
          <h1 className="text-white text-2xl sm:text-3xl font-bold">{restaurant.name}</h1>
        </div>
      </div>

      {/* Search & Filters */}
      <div className="p-4 bg-white shadow-md">
        <div className="flex items-center gap-3">
          {/* Search Bar */}
          <div className="relative flex-1">
            <input
              type="text"
              placeholder="Search menu..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-full border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          </div>

          {/* Veg / Non-Veg Filter */}
          <div className="flex gap-2">
            {["all", "veg", "non-veg"].map((type) => (
              <button
                key={type}
                onClick={() => setFilterType(type)}
                className={`px-3 py-1.5 rounded-full text-sm font-medium ${
                  filterType === type ? "bg-blue-600 text-white" : "bg-gray-200"
                }`}
              >
                {type === "all" ? "All" : type.charAt(0).toUpperCase() + type.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Category Filter Buttons */}
      <div className="overflow-x-auto bg-white py-3 px-4 shadow-sm">
        <div className="flex gap-3">
          <button
            onClick={() => setActiveCategory("all")}
            className={`px-4 py-2 rounded-full text-sm font-medium ${
              activeCategory === "all" ? "bg-blue-600 text-white" : "bg-gray-100 hover:bg-gray-200"
            }`}
          >
            All
          </button>
          {categories.map((category) => (
            <button
              key={category._id}
              onClick={() => setActiveCategory(category._id)}
              className={`px-4 py-2 rounded-full text-sm font-medium ${
                activeCategory === category._id ? "bg-blue-600 text-white" : "bg-gray-100 hover:bg-gray-200"
              }`}
            >
              {category.categoryName}
            </button>
          ))}
        </div>
      </div>

      {/* Menu Categories */}
      <div className="max-w-6xl mx-auto p-4">
        {filteredCategories.map((category) => (
          <div key={category._id} className="mb-6">
            <h2 className="text-lg font-bold text-gray-800 mb-3">{category.categoryName}</h2>

            {/* Main Items */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {category.items.map((item) => (
                <MenuItem key={item._id} item={item} />
              ))}
            </div>

            {/* Subcategories */}
            {category.subcategories?.map((subcategory) => (
              <div key={subcategory._id} className="mt-4">
                <h3 className="text-md font-semibold text-gray-700 mb-2">{subcategory.subcategoryName}</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {subcategory.items.map((item) => (
                    <MenuItem key={item._id} item={item} />
                  ))}
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

const MenuItem = ({ item }) => (
    <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition">
      <div className="flex gap-4 p-4">
        {/* Image */}
        <div className="w-24 h-24 sm:w-32 sm:h-32 flex-shrink-0 relative">
          <img src={item.subimage} alt={item.title} className="w-full h-full object-cover rounded-lg" />
          
          {/* Veg/Non-Veg Icon */}
          <div className="absolute top-2 right-2">
            <span className={`inline-flex items-center justify-center w-5 h-5 rounded-full border-2 ${
              item.type === "veg" ? "border-green-600" : "border-red-600"
            }`}>
              <span className={`w-2.5 h-2.5 rounded-full ${
                item.type === "veg" ? "bg-green-600" : "bg-red-600"
              }`}></span>
            </span>
          </div>
        </div>
  
        {/* Content */}
        <div className="flex-1 min-w-0">
          <h3 className="text-base font-medium text-gray-900">{item.title}</h3>
          <p className="text-sm text-gray-500 truncate">{item.detail}</p>
          <p className="text-base font-semibold text-gray-900">₹{item.price}</p>
        </div>
      </div>
    </div>
  );
  

export default Normalmenu;
