import React, { useState, useContext, useEffect } from 'react';
import { postLoginData, googleAuth } from '../../api'
import { toast } from 'react-hot-toast';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { UserDataContext } from '../../context/UserContext';
import { RestaurantDataContext } from '../../context/RestaurantContext';
import { Loader2 } from 'lucide-react';
import InputField from '../../components/InputFieldValidation/InputField';
import { Button } from '../../components/ui/button';
import { GoogleLogin } from '@react-oauth/google';

const Login = () => {
  const { user, setUser } = useContext(UserDataContext);
  const { restaurantData, setRestaurantData } = useContext(RestaurantDataContext);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (localStorage.getItem('token') && user) {
      redirectBasedOnRole(user);
    }
  }, [user, navigate]);

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    role: 'admin'
  });

  const redirectBasedOnRole = (userData) => {
    if (userData.role === 'admin') {
      navigate('/');
    } else if (userData.role === 'waiter' || userData.role === 'chef') {
      navigate('/staff/dashboard');
    }
  };

  const handleGoogleSuccess = async (credentialResponse) => {
    try {
      setLoading(true);
      const response = await googleAuth(credentialResponse.credential);

      if (response.data.success) {
        toast.success(response.data.message);
        setUser(response.data.user);
        localStorage.setItem('token', response.data.token);

        if (response.data.restaurant) {
          setRestaurantData(response.data.restaurant);
        }

        redirectBasedOnRole(response.data.user);
      }
    } catch (error) {
      toast.error(error?.response?.data?.message || 'Google authentication failed');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleError = () => {
    toast.error('Google sign in was unsuccessful');
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.username) {
      newErrors.username = 'Username is required';
    } else if (formData.username.length < 3) {
      newErrors.username = 'Username must be at least 3 characters';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{6,}$/.test(formData.password)) {
      newErrors.password = 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      if (validateForm()) {
        setLoading(true);
        const response = await postLoginData(formData);

        if (response.data.success) {
          console.log(response.data);
          const userRole = response.data.user.role;
          const normalizedRole = userRole === 'admin' ? 'admin' : 'staff';
          if (formData.role !== normalizedRole) {
            toast.error('Invalid role selected');
            return;
          }
          toast.success(response.data.message);
          setUser(response.data.user);
          localStorage.setItem('token', response.data.token);

          if (response.data.restaurant) {
            setRestaurantData(response.data.restaurant);
          }

          redirectBasedOnRole(response.data.user);
        }
      }
    } catch (error) {
      toast.error(error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    if (errors[e.target.name]) {
      setErrors({
        ...errors,
        [e.target.name]: ''
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FFF5E6] to-[#FFE5CC] py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="bg-white rounded-3xl shadow-2xl overflow-hidden">
          {/* Header */}
          <div className="px-6 py-8 text-center">
            <div className="flex items-center justify-center gap-2 mb-6">
              <svg className="w-10 h-10 text-[#EFA052]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v1m6 11h2m-6 0h-2m0 0H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h1 className="text-3xl font-bold text-[#EFA052]">Scan2Menu</h1>
            </div>
            <h2 className="text-2xl font-semibold text-gray-800">Welcome Back!</h2>
            <p className="mt-2 text-gray-600">Access your digital menu dashboard</p>
          </div>

          {/* Role Selection */}

          <div className="px-6">
            <div className="flex gap-4 mb-6">
              <button
                type="button"
                onClick={() => setFormData({ ...formData, role: 'admin' })}
                className={`flex-1 py-3 px-4 rounded-xl font-medium transition-all ${formData.role === 'admin'
                  ? 'bg-[#EFA052] text-white shadow-lg'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'

                  }`}
              >
                <div className="flex items-center justify-center gap-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                  Admin
                </div>
              </button>
              <button
                type="button"
                onClick={() => setFormData({ ...formData, role: 'staff' })}
                className={`flex-1 py-3 px-4 rounded-xl font-medium transition-all ${formData.role === 'staff'
                  ? 'bg-[#EFA052] text-white shadow-lg'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'

                  }`}
              >
                <div className="flex items-center justify-center gap-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Staff
                </div>
              </button>
            </div>
          </div>





          {/* Form */}
          <form onSubmit={handleSubmit} className="px-6 pb-8">
            <div className="space-y-4">
              <InputField
                label="Username"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                error={errors.username}
                placeholder="Enter your username"
                className="rounded-xl border-gray-300 focus:border-[#EFA052] focus:ring focus:ring-[#EFA052] focus:ring-opacity-50"
              />

              <InputField
                label="Password"
                type="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                error={errors.password}
                placeholder="Enter your password"
                className="rounded-xl border-gray-300 focus:border-[#EFA052] focus:ring focus:ring-[#EFA052] focus:ring-opacity-50"
              />

              <div className="flex items-center justify-between">
                <label className="flex items-center">
                  <input type="checkbox" className="rounded border-gray-300 text-[#EFA052] focus:ring-[#EFA052]" />
                  <span className="ml-2 text-sm text-gray-600">Remember me</span>
                </label>
                <Link to="/forgot-password" className="text-sm text-[#EFA052] hover:text-[#D88A3B]">
                  Forgot password?
                </Link>
              </div>

              <Button
                type="submit"
                className="w-full py-3 font-medium text-white bg-[#EFA052] hover:bg-[#D88A3B] rounded-xl transition-colors"
                disabled={loading}
              >
                {loading ? (
                  <div className="flex items-center justify-center gap-2">
                    <Loader2 className="animate-spin" size={20} />
                    <span>Logging in...</span>
                  </div>
                ) : (
                  'Login'
                )}
              </Button>
              {formData.role === 'admin' && (<>
                <div className="relative my-6">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">Or continue with</span>
                  </div>
                </div>

                <div className="flex justify-center">
                  <GoogleLogin
                    onSuccess={handleGoogleSuccess}
                    onError={handleGoogleError}
                    useOneTap
                  />
                </div>
            <p className="mt-6 text-center text-sm text-gray-600">
              Don't have an account?{' '}
              <Link to="/signup" className="text-[#EFA052] hover:text-[#D88A3B] font-semibold">
                Sign Up
              </Link>
            </p>
              </>)}
            </div>


          </form>
        </div>
      </div>
    </div>
  );
};

export default Login;
