import React, { useState, useEffect, useRef, useContext } from "react";
import InputField from "../../components/InputFieldValidation/InputField";
import {
  ShoppingCart,
  Plus,
  Minus,
  Search,
  Clock,
  History,
  X,
} from "lucide-react";
import { UserDataContext } from "../../context/UserContext";
import { toast } from "react-hot-toast";
import Navbar from "./Navbar";
import { getwholeMenu, createOrderByWaiter, getAllTables } from "../../api";

const WaiterMenu = () => {
  const { user } = useContext(UserDataContext);
  const slug = user?.restaurant_detail.slug;
  const [menuItems, setMenuItems] = useState([
    {
      _id: "cat1",
      categoryName: "Pizza",
      items: [
        {
          _id: "1",
          title: "Margherita Pizza",
          price: 12.99,
          type: "veg",
          detail: "Fresh tomatoes, mozzarella, basil",
          subimage:
            "https://images.unsplash.com/photo-1593560708920-61dd98c46a4e?w=500&auto=format",
          menuButton: true,
        },
        {
          _id: "2",
          title: "Pepperoni Pizza",
          price: 14.99,
          type: "non-veg",
          detail: "Classic pepperoni with cheese",
          subimage:
            "https://images.unsplash.com/photo-1628840042765-356cda07504e?w=500&auto=format",
          menuButton: true,
        },
      ],
      subcategories: [],
    },
    {
      _id: "cat2",
      categoryName: "Burgers",
      items: [
        {
          _id: "3",
          title: "Veggie Burger",
          price: 9.99,
          type: "veg",
          detail: "Plant-based patty with veggies",
          subimage:
            "https://images.unsplash.com/photo-1550317138-10000687a72b?w=500&auto=format",
          menuButton: true,
        },
        {
          _id: "4",
          title: "Chicken Burger",
          price: 11.99,
          type: "non-veg",
          detail: "Grilled chicken with sauce",
          subimage:
            "https://images.unsplash.com/photo-1513185158878-8d8c2a2a3da3?w=500&auto=format",
          menuButton: true,
        },
      ],
      subcategories: [],
    },
  ]);

  const [cartItems, setCartItems] = useState(() => {
    const savedCartItems = JSON.parse(localStorage.getItem("waiter-cartItems"));
    return savedCartItems?.filter(item => item.restaurantSlug === slug) || [];
  });
  const [tables, setTables] = useState([]);
  const [showOrderForm, setShowOrderForm] = useState(false);
  const [showOrderSummary, setShowOrderSummary] = useState(false);
  const [showOrderHistory, setShowOrderHistory] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [orderHistory, setOrderHistory] = useState(() => {
    const savedOrderHistory = localStorage.getItem("waiter-orderHistory");
    return savedOrderHistory ? JSON.parse(savedOrderHistory) : [];
  });
  const [filterType, setFilterType] = useState("all");
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [orderDetails, setOrderDetails] = useState({
    name: "",
    phoneNumber: "",
    tableNumber: "",
  });
  useEffect(() => {
    console.log(orderDetails)
  }, [orderDetails])
  const [loading, setLoading] = useState(false);
  const [selectedExtrasMap, setSelectedExtrasMap] = useState({});
  const [errors, setErrors] = useState({});
  const [modifyingOrderId, setModifyingOrderId] = useState(null);
  const restaurantRef = useRef({});
  const totalITemCount = useRef({
    nonVegetarian: 0,
    total: 0,
    vegetarian: 0,
  });

  useEffect(() => {
    if (user) {
      setOrderDetails((prev) => ({
        ...prev,
        name: user.name || "",
        phoneNumber: user.phone || "",
      }));
    }
  }, [user]);
  useEffect(() => {
    async function getMenu(Rslug) {
      try {
        const response = await getwholeMenu(Rslug);
        if (response.data.success) {
          console.log("response", response);
          restaurantRef.current = response?.data?.restaurant;
          totalITemCount.current = response?.data?.itemCounts;
          setMenuItems(response?.data?.data);
        }
      } catch (error) {
        toast.error(error?.response?.data?.message || error.message);
      }
    }
    if (slug) {
      getMenu(slug);
    }
  }, [slug]);

  useEffect(() => {
    const savedCartItems = JSON.parse(localStorage.getItem("waiter-cartItems")) || [];
    const updatedCartItems = [
      ...savedCartItems.filter(item => item.restaurantSlug !== slug), // Keep other restaurant's items
      ...cartItems // Update only current restaurant's items
    ];

    localStorage.setItem('waiter-cartItems', JSON.stringify(updatedCartItems));
  }, [cartItems]);

  useEffect(() => {
    localStorage.setItem("waiter-orderHistory", JSON.stringify(orderHistory));
  }, [orderHistory]);

  const getFilteredItems = () => {
    return menuItems
      ?.map((category) => {
        const filteredSubcategories = category.subcategories
          ?.map((subcategory) => {
            const subcategoryMatchesSearch =
              searchQuery === "" ||
              subcategory.subcategoryName
                .toLowerCase()
                .includes(searchQuery.toLowerCase());

            const filteredItems = subcategory.items.filter((item) => {
              const matchesType =
                filterType === "all"
                  ? true
                  : filterType === "veg"
                    ? item.type === "veg"
                    : item.type === "non-veg";

              const matchesSearch =
                searchQuery === "" ||
                item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                item.detail.toLowerCase().includes(searchQuery.toLowerCase());

              return matchesType && (matchesSearch || subcategoryMatchesSearch);
            });

            return { ...subcategory, items: filteredItems };
          })
          .filter((subcategory) => subcategory.items.length > 0); // Remove empty subcategories

        // Filter category-level items
        const filteredCategoryItems = category.items.filter((item) => {
          const matchesType =
            filterType === "all"
              ? true
              : filterType === "veg"
                ? item.type === "veg"
                : item.type === "non-veg";

          const matchesSearch =
            searchQuery === "" ||
            item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            item.detail.toLowerCase().includes(searchQuery.toLowerCase());

          return matchesType && matchesSearch;
        });

        if (
          filteredCategoryItems.length > 0 ||
          filteredSubcategories.length > 0
        ) {
          return {
            ...category,
            items: filteredCategoryItems,
            subcategories: filteredSubcategories,
          };
        }

        return null;
      })
      .filter((category) => category !== null);
  };
  useEffect(() => {
    const getTables = async (slug) => {
      try {
        const response = await getAllTables(slug);
        if (response.data.success) {
          setTables(response.data.data);
        }
      } catch (error) {
        toast.error(error?.response?.data?.message || error.message);
      }
    };
    if (slug) {
      getTables(slug);
    }
  }, [orderHistory, slug]);
  const filteredCategories = getFilteredItems();

  const handleExtraChange = (itemId, extra, isChecked) => {
    setSelectedExtrasMap(prevState => {
      const updatedExtras = isChecked
        ? [...(prevState[itemId] || []), extra]  // Add extra
        : prevState[itemId]?.filter(e => e._id !== extra._id) || []; // Remove extra

      return { ...prevState, [itemId]: updatedExtras };
    });
  };
  const addToCart = (item, selectedExtras = []) => {
    console.log("Item added:", item, "Selected extras:", selectedExtras);

    // Create a unique key based on item ID + sorted extras IDs
    const itemKey =
      item._id +
      (selectedExtras.length
        ? `-${selectedExtras
          .map((e) => e._id)
          .sort()
          .join("-")}`
        : "");
    console.log(itemKey);
    // Check if an item with the same ID and extras exists in the cart
    const existingItem = cartItems.find((cartItem) => {
      const existingKey =
        cartItem._id +
        (cartItem.selectedExtras.length
          ? `-${cartItem.selectedExtras
            .map((e) => e._id)
            .sort()
            .join("-")}`
          : "");
      return existingKey === itemKey;
    });

    if (existingItem) {
      // If item with same extras exists, increase quantity
      setCartItems(
        cartItems.map((cartItem) =>
          cartItem === existingItem
            ? { ...cartItem, quantity: cartItem.quantity + 1 }
            : cartItem
        )
      );
    } else {
      // If new combination, add as a separate entry
      setCartItems([
        ...cartItems,
        { ...item, quantity: 1, selectedExtras, itemKey, restaurantSlug: slug },
      ]);
    }
  };
  const handleExtraToggle = (itemKey, extra, isChecked) => {
    setCartItems(
      cartItems.map((item) => {
        if (item.itemKey === itemKey) {
          let updatedExtras = isChecked
            ? [...item.selectedExtras, extra] // Add extra
            : item.selectedExtras.filter((e) => e._id !== extra._id); // Remove extra

          return { ...item, selectedExtras: updatedExtras };
        }
        return item;
      })
    );
  };

  const removeFromCart = (itemKey) => {
    setCartItems(
      cartItems
        .map((item) =>
          item.itemKey === itemKey && item.quantity > 0
            ? { ...item, quantity: item.quantity - 1 }
            : item
        )
        .filter((item) => item.quantity > 0)
    );
  };

  const calculateTotal = () => {
    const subtotal = cartItems.reduce((sum, item) => {
      const itemTotal =
        (item.price +
          item?.selectedExtras?.reduce(
            (extraSum, extra) => extraSum + extra.price,
            0
          )) *
        item.quantity;
      return sum + itemTotal;
    }, 0);

    const gst = subtotal * 0.18;
    return { subtotal, gst, total: subtotal + gst };
  };

  const handleOrder = () => {
    if (cartItems.length === 0) {
      toast.error("Your cart is empty!");
      return;
    }
    setShowOrderForm(true);
  };

  const validate = () => {
    let newErrors = {};
    if (!orderDetails.name)
      newErrors.name = "Waiter Name is required";
    if (!orderDetails.phoneNumber)
      newErrors.phoneNumber = "Phone Number is required";
    else if (!/^\d{10}$/.test(orderDetails.phoneNumber))
      newErrors.phoneNumber = "Phone Number must be 10 digits";
    if (!orderDetails.tableNumber)
      newErrors.tableNumber = "Table Number is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleOrderDetailFormChange = (e) => {
    const { name, value } = e.target;
    setOrderDetails({ ...orderDetails, [name]: value });
    if (errors[name]) setErrors({ ...errors, [name]: "" });
  };

  const handlePlaceOrder = async () => {
    if (!validate()) return;
    try {
      const payload = {
        ...orderDetails,
        subtotal: calculateTotal().subtotal,
        gst: calculateTotal().gst,
        total: calculateTotal().total,
        restaurantId: restaurantRef.current.id,
        items: cartItems.map(item => ({
          itemId: item._id,
          quantity: item.quantity,
          selectedExtras: item.selectedExtras.map(extra => ({
            _id: extra._id,
            title: extra.title,
            price: extra.price,
          })),
        }))

      }
      console.log(payload)

      const response = await createOrderByWaiter(payload);
      console.log(response);
      if (response.data.success) {
        console.log("ct", response.data);
        setLoading(true);
        setShowOrderForm(false);
        setShowOrderSummary(false);
        setOrderDetails({
          name: '',
          phoneNumber: '',
          tableNumber: '',
        })
        setOrderHistory((prev) => [{
          _id: response?.data?.order?._id,
          date: response?.data?.order?.createdAt,
          status: response?.data?.order?.status,
          items: response?.data?.order?.items,
          total: response?.data?.order?.total,
          gst: response?.data?.order?.gst,
          selectedExtras: response?.data?.order?.selectedExtras,
        }, ...prev]);
        clearCart();
        setShowOrderHistory(true);
        toast.success('Order placed successfully!');

      }
    } catch (error) {
      console.log(error)
      toast.error(error?.response?.data?.message || error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCompleteOrder = (orderId, paymentMethod) => {
    setOrderHistory(
      orderHistory.map((order) =>
        order._id === orderId
          ? { ...order, status: "Completed", paymentMethod }
          : order
      )
    );
    toast.success(`Order ${orderId} completed with ${paymentMethod}!`);
  };

  const handleCheckout = () => {
    setShowOrderSummary(true);
  };

  const clearCart = () => {
    setCartItems([]);
  };

  const getCartItemsCount = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  };

  const handleAddItemsToOrder = (orderId) => {
    const order = orderHistory.find((o) => o._id === orderId);
    if (order) {
      setCartItems(order.items.map((item) => ({ ...item })));
      setModifyingOrderId(orderId);
      setShowOrderHistory(false);
    }
  };

  const handleUpdateOrder = () => {
    if (cartItems.length === 0) {
      toast.error("Your cart is empty!");
      return;
    }
    const orderToUpdate = orderHistory.find(
      (order) => order._id === modifyingOrderId
    );
    if (orderToUpdate) {
      const updatedItems = cartItems;
      const { subtotal, gst, total } = calculateTotal();
      setOrderHistory(
        orderHistory.map((order) =>
          order._id === modifyingOrderId
            ? { ...order, items: updatedItems, subtotal, gst, total }
            : order
        )
      );
      setCartItems([]);
      setModifyingOrderId(null);
      setShowOrderSummary(false);
      toast.success("Order updated successfully!");
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="max-w-6xl mx-auto p-4 pb-24">
        {/* Restaurant Header */}
        <div className="bg-white rounded-lg shadow-lg mb-6 overflow-hidden">
          <div className="relative h-48">
            <img
              src={restaurantRef.current.image}
              alt={restaurantRef.current.name}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
            <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
              <div className="flex justify-between items-start">
                <div>
                  <h1 className="text-3xl font-bold">
                    {restaurantRef.current.name}
                  </h1>
                  <div className="flex items-center mt-2">
                    <span className="text-yellow-400">★</span>
                    <span className="ml-1">4.8</span>
                  </div>
                </div>
                <button
                  onClick={() => setShowOrderHistory(true)}
                  className="flex items-center gap-2 bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg backdrop-blur-sm transition-all"
                >
                  <History className="w-5 h-5" />
                  Order History
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Filter Section */}
        <div className="bg-white rounded-lg shadow-lg mb-6">
          <div className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative w-full sm:w-2/5">
                <input
                  type="text"
                  placeholder="Search menu items..."
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    if (setSelectedCategory) {
                      setSelectedCategory(null);
                    }
                  }}
                  className="w-full pl-10 pr-4 h-10 rounded-lg border border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              </div>
              <div className="flex gap-2 sm:gap-3 flex-1 justify-end">
                {/* All Items Button */}
                <button
                  onClick={() => setFilterType('all')}
                  className={`flex items-center px-3 py-2 rounded-lg transition-all ${filterType === 'all'
                    ? 'bg-gray-800 text-white shadow-sm'
                    : 'bg-gray-100 hover:bg-gray-200'
                    }`}
                >
                  <span className="text-sm whitespace-nowrap">All ({totalITemCount.current.total || 0})</span>
                </button>

                {/* Veg Button */}
                <button
                  onClick={() => setFilterType('veg')}
                  className={`flex items-center px-3 py-2 rounded-lg transition-all ${filterType === 'veg'
                    ? 'bg-green-600 text-white shadow-sm'
                    : 'bg-gray-100 hover:bg-gray-200'
                    }`}
                >
                  <span className="w-3 h-3 rounded-full border-2 border-current flex items-center justify-center mr-1.5">
                    <span className="w-1.5 h-1.5 rounded-full bg-current" />
                  </span>
                  <span className="text-sm whitespace-nowrap">Veg ({totalITemCount.current.vegetarian || 0})</span>
                </button>

                {/* Non-Veg Button */}
                <button
                  onClick={() => setFilterType('non-veg')}
                  className={`flex items-center px-3 py-2 rounded-lg transition-all ${filterType === 'non-veg'
                    ? 'bg-red-600 text-white shadow-sm'
                    : 'bg-gray-100 hover:bg-gray-200'
                    }`}
                >
                  <span className="w-3 h-3 rounded-full border-2 border-current flex items-center justify-center mr-1.5">
                    <span className="w-1.5 h-1.5 rounded-full bg-current" />
                  </span>
                  <span className="text-sm whitespace-nowrap">Non-Veg ({totalITemCount.current.nonVegetarian || 0})</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Categories Section */}
        <div className="container mx-auto px-4 py-6">
          {!selectedCategory && (
            <div>
              <h1 className="text-2xl font-bold text-gray-800 mb-6">
                Our Menu
              </h1>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredCategories?.map((category) => (
                  <div
                    key={category._id}
                    onClick={() => setSelectedCategory(category)}
                    className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                  >
                    <div className="p-6">
                      <h2 className="text-xl font-semibold text-gray-800 text-center">
                        {category.categoryName}
                      </h2>
                      <p className="text-gray-500 text-sm text-center mt-2">
                        {category.items.length +
                          category.subcategories?.reduce(
                            (sum, sub) => sum + sub?.items?.length,
                            0
                          )}{" "}
                        varieties available
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {selectedCategory && (
            <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <button
                onClick={() => setSelectedCategory(null)}
                className="flex items-center text-gray-600 hover:text-gray-800 mb-4 sm:mb-6 p-2 -ml-2"
              >
                <svg
                  className="w-6 h-6 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
                <span className="text-base sm:text-lg">Back to Categories</span>
              </button>
              <div className="mb-6 sm:mb-8">
                <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                  <div className="px-4 py-6 sm:px-6 sm:py-8">
                    <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 text-center">
                      {selectedCategory.categoryName}
                    </h1>
                  </div>
                </div>
              </div>
              <div className="space-y-4 sm:space-y-6">

                <div className="space-y-4 sm:space-y-6">
                  {selectedCategory.items.map((item) => (
                    <div
                      key={item?._id}
                      className="bg-white rounded-xl shadow-sm overflow-hidden"
                    >
                      <div className="p-4 sm:p-6">
                        {/* Item Card - Flexible layout for mobile */}
                        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-6">
                          {/* Image and Details Container */}
                          <div className="flex flex-row items-start gap-4 w-full sm:w-auto">
                            <img
                              src={item?.subimage}
                              alt={item?.title}
                              className="w-24 h-24 sm:w-32 sm:h-32 object-cover rounded-lg flex-shrink-0"
                            />
                            <div className="flex-grow">
                              <div className="flex items-start gap-2 flex-wrap">
                                <h3 className="text-lg sm:text-xl font-semibold text-gray-800">
                                  {item.title || item.name}
                                </h3>
                                {/* Veg/Non-veg indicator */}
                                <span
                                  className={`inline-flex items-center justify-center w-5 h-5 rounded-full border-2 ${item?.type === "veg"
                                    ? "border-green-600"
                                    : "border-red-600"
                                    }`}
                                >
                                  <span
                                    className={`w-2.5 h-2.5 rounded-full ${item?.type === "veg"
                                      ? "bg-green-600"
                                      : "bg-red-600"
                                      }`}
                                  ></span>
                                </span>
                              </div>
                              <p className="text-gray-600 text-sm sm:text-base mt-1">
                                {item?.detail}
                              </p>
                              <div>
                                {item?.extras &&
                                  item?.extras.map((extra) => (
                                    <p key={extra._id}>
                                      <label className="text-sm text-gray-500">
                                        <input
                                          type="checkbox"
                                          checked={
                                            selectedExtrasMap[item._id]?.some(
                                              (e) => e._id === extra._id
                                            ) || false
                                          }
                                          onChange={(e) =>
                                            handleExtraChange(
                                              item._id,
                                              extra,
                                              e.target.checked
                                            )
                                          }
                                        />
                                        {extra.title} &nbsp; &nbsp;{" "}
                                        <span className="text-gray-900">
                                          ${extra.price}
                                        </span>
                                      </label>
                                    </p>
                                  ))}
                              </div>
                              <p className="text-gray-900 font-medium text-lg sm:text-xl mt-2">
                                ${item?.price.toFixed(2)}
                              </p>
                            </div>
                          </div>

                          {/* Add Button - Full width on mobile */}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              item?.menuButton === true
                                ? addToCart(
                                  item,
                                  selectedExtrasMap[item._id] || []
                                )
                                : toast.error(
                                  "This item is not currently available"
                                );
                            }}
                            className={`w-full sm:w-auto px-4 py-2.5 sm:py-3 rounded-lg text-white text-base sm:text-lg font-medium transition-colors ${item?.menuButton === true
                              ? "bg-blue-600 hover:bg-blue-700 active:bg-blue-800"
                              : "bg-blue-300 cursor-not-allowed"
                              }`}
                          >
                            Add to Cart
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Subcategories Section */}
                  {selectedCategory.subcategories.map((subcategory) => (
                    <div
                      key={subcategory?._id}
                      className="bg-white rounded-xl shadow-sm overflow-hidden"
                    >
                      {/* Subcategory Header */}
                      <h2 className="text-xl sm:text-2xl font-semibold text-gray-800 p-4 sm:p-6 border-b">
                        {subcategory?.subcategoryName}
                      </h2>

                      {/* Subcategory Items */}
                      <div className="divide-y divide-gray-100">
                        {subcategory.items.map((item) => (
                          <div
                            key={item?._id}
                            className="bg-white rounded-xl shadow-sm overflow-hidden"
                          >
                            <div className="p-4 sm:p-6">
                              {/* Item Card - Flexible layout for mobile */}
                              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-6">
                                {/* Image and Details Container */}
                                <div className="flex flex-row items-start gap-4 w-full sm:w-auto">
                                  <img
                                    src={item?.subimage}
                                    alt={item?.title}
                                    className="w-24 h-24 sm:w-32 sm:h-32 object-cover rounded-lg flex-shrink-0"
                                  />
                                  <div className="flex-grow">
                                    <div className="flex items-start gap-2 flex-wrap">
                                      <h3 className="text-lg sm:text-xl font-semibold text-gray-800">
                                        {item.title || item.name}
                                      </h3>
                                      {/* Veg/Non-veg indicator */}
                                      <span
                                        className={`inline-flex items-center justify-center w-5 h-5 rounded-full border-2 ${item?.type === "veg"
                                          ? "border-green-600"
                                          : "border-red-600"
                                          }`}
                                      >
                                        <span
                                          className={`w-2.5 h-2.5 rounded-full ${item?.type === "veg"
                                            ? "bg-green-600"
                                            : "bg-red-600"
                                            }`}
                                        ></span>
                                      </span>
                                    </div>
                                    <p className="text-gray-600 text-sm sm:text-base mt-1">
                                      {item?.detail}
                                    </p>
                                    <div>
                                      {item?.extras &&
                                        item?.extras.map((extra) => (
                                          <p key={extra._id}>
                                            <label className="text-sm text-gray-500">
                                              <input
                                                type="checkbox"
                                                checked={
                                                  selectedExtrasMap[
                                                    item._id
                                                  ]?.some(
                                                    (e) => e._id === extra._id
                                                  ) || false
                                                }
                                                onChange={(e) =>
                                                  handleExtraChange(
                                                    item._id,
                                                    extra,
                                                    e.target.checked
                                                  )
                                                }
                                              />
                                              {extra.title} &nbsp; &nbsp;{" "}
                                              <span className="text-gray-900">
                                                ${extra.price}
                                              </span>
                                            </label>
                                          </p>
                                        ))}
                                    </div>
                                    <p className="text-gray-900 font-medium text-lg sm:text-xl mt-2">
                                      ${item?.price.toFixed(2)}
                                    </p>
                                  </div>
                                </div>

                                {/* Add Button - Full width on mobile */}
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    item?.menuButton === true
                                      ? addToCart(
                                        item,
                                        selectedExtrasMap[item._id] || []
                                      )
                                      : toast.error(
                                        "This item is not currently available"
                                      );
                                  }}
                                  className={`w-full sm:w-auto px-4 py-2.5 sm:py-3 rounded-lg text-white text-base sm:text-lg font-medium transition-colors ${item?.menuButton === true
                                    ? "bg-blue-600 hover:bg-blue-700 active:bg-blue-800"
                                    : "bg-blue-300 cursor-not-allowed"
                                    }`}
                                >
                                  Add to Cart
                                </button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Fixed Bottom Cart Preview */}
      {cartItems.length > 0 &&
        !showOrderSummary &&
        !showOrderHistory &&
        !showOrderForm && (
          <div className="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t z-40">
            <div className="max-w-6xl mx-auto p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center">
                    {getCartItemsCount()}
                  </div>
                  <span className="font-medium">
                    ${calculateTotal().total.toFixed(2)}
                  </span>
                </div>
                <button
                  onClick={() => setShowOrderSummary(true)}
                  className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg flex items-center gap-2"
                >
                  <ShoppingCart className="w-5 h-5" />
                  Checkout
                </button>
              </div>
            </div>
          </div>
        )}

      {/* Order Summary Modal */}
      {showOrderSummary && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b sticky top-0 bg-white">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-bold">
                  {modifyingOrderId ? "Modify Order" : "Order Summary"}
                </h2>
                <button
                  onClick={() => setShowOrderSummary(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
              {modifyingOrderId && (
                <p className="text-sm text-gray-500 mt-2">
                  Adding items to Order for Table{" "}
                  {
                    orderHistory.find((o) => o._id === modifyingOrderId)
                      ?.tableNumber
                  }
                </p>
              )}
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {cartItems.map((item) => (
                  <div
                    key={item.itemKey}
                    className="flex justify-between items-center py-4 border-b"
                  >
                    <div className="flex gap-4">
                      <img
                        src={item.subimage}
                        alt={item.title}
                        className="w-16 h-16 rounded-lg object-cover"
                      />
                      <div>
                        <h3 className="font-medium">{item.title}</h3>
                        <div>
                          {item.extras &&
                            item.extras.map((extra) => (
                              <div
                                key={extra._id}
                                className="flex items-center gap-2"
                              >
                                <input
                                  type="checkbox"
                                  checked={item.selectedExtras.some(
                                    (e) => e._id === extra._id
                                  )}
                                  onChange={(e) =>
                                    handleExtraToggle(
                                      item.itemKey,
                                      extra,
                                      e.target.checked
                                    )
                                  }
                                />
                                <span className="text-sm text-gray-500">
                                  {extra.title} (${extra.price.toFixed(2)})
                                </span>
                              </div>
                            ))}
                        </div>
                        <div className="flex items-center mt-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              removeFromCart(item.itemKey);
                            }}
                            className="p-1 rounded border border-gray-200 hover:bg-gray-100"
                          >
                            <Minus className="w-4 h-4" />
                          </button>
                          <span className="mx-3">{item.quantity}</span>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              addToCart(item, item.selectedExtras);
                            }}
                            className="p-1 rounded border border-gray-200 hover:bg-gray-100"
                          >
                            <Plus className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        $
                        {(
                          (item.price +
                            item.selectedExtras.reduce(
                              (sum, extra) => sum + extra.price,
                              0
                            )) *
                          item.quantity
                        ).toFixed(2)}
                      </p>
                      <p className="text-sm text-gray-500">
                        ${item.price.toFixed(2)} each
                      </p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6 space-y-4">
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>${calculateTotal().subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>GST (18%)</span>
                  <span>${calculateTotal().gst.toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-bold text-lg pt-4 border-t">
                  <span>Total</span>
                  <span>${calculateTotal().total.toFixed(2)}</span>
                </div>
              </div>
              <div className="flex gap-4 mt-6">
                <button
                  onClick={() => setShowOrderSummary(false)}
                  className="flex-1 py-3 px-4 border rounded-lg hover:bg-gray-50"
                >
                  Continue Adding
                </button>
                {modifyingOrderId ? (
                  <button
                    onClick={handleUpdateOrder}
                    className="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg"
                  >
                    Update Order
                  </button>
                ) : (
                  <button
                    onClick={handleOrder}
                    className="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg"
                  >
                    Place Order
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Order History Modal */}
      {showOrderHistory && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b sticky top-0 bg-white">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-bold">Order History</h2>
                <button
                  onClick={() => setShowOrderHistory(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                {orderHistory.map((order) => (
                  <div key={order._id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <p className="font-medium">Order #{order._id}</p>
                        <p className="text-sm text-gray-500">
                          {new Date(order.date).toLocaleString()}
                        </p>
                        <p className="text-sm text-gray-500">
                          Waiter: {order.waiterName}{" "}
                        </p>
                      </div>
                      <span
                        className={`px-3 py-1 rounded-full text-sm ${order.status === "Completed"
                          ? "bg-green-100 text-green-800"
                          : "bg-yellow-100 text-yellow-800"
                          }`}
                      >
                        {order.status}
                      </span>
                    </div>
                    {order.items &&
                      order.items.map((item) => {
                        // Calculate extra price total
                        const extrasTotal = item.selectedExtras
                          ? item.selectedExtras.reduce(
                            (sum, extra) => sum + extra.price,
                            0
                          )
                          : 0;

                        // Calculate total item price (including extras)
                        const itemTotal =
                          (item.itemId.price + extrasTotal) * item.quantity;

                        return (
                          <div
                            key={item._id}
                            className="flex justify-between py-2 border-b"
                          >
                            <div>
                              <span className="font-medium">
                                {item.itemId.title}
                              </span>
                              {item.selectedExtras &&
                                item.selectedExtras.map((extra) => (
                                  <div
                                    key={extra._id}
                                    className="flex items-center gap-2 text-sm text-gray-500"
                                  >
                                    <span>{extra.title}</span>
                                    <span>${extra.price?.toFixed(2)}</span>
                                  </div>
                                ))}
                            </div>
                            <div className="text-right">
                              <span>
                                ${item.itemId.price?.toFixed(2)} x{" "}
                                {item.quantity}
                              </span>
                              {extrasTotal > 0 && (
                                <p className="text-sm text-gray-500">
                                  + Extras: ${extrasTotal?.toFixed(2)}
                                </p>
                              )}
                              <p className="text-sm font-medium">
                                Total: ${itemTotal?.toFixed(2)}
                              </p>
                            </div>
                          </div>
                        );
                      })}
                    <div className="mt-4">
                      <div className="flex justify-between">
                        <span>Subtotal</span>
                        <span>${order.subtotal?.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>GST (18%)</span>
                        <span>${order.gst?.toFixed(2)}</span>
                      </div>
                    </div>
                    <div className="mt-4 pt-4">
                      <div className="flex justify-between font-medium">
                        <span>Total Amount</span>
                        <span>${order.total?.toFixed(2)}</span>
                      </div>
                      {order.paymentMethod && (
                        <p className="text-sm text-gray-500 mt-2">
                          Paid via {order.paymentMethod?.toUpperCase()}
                        </p>
                      )}
                    </div>
                    {order.status === "Pending" && (
                      <div className="mt-4 flex gap-2">
                        <button
                          onClick={() => handleCompleteOrder(order._id, "cash")}
                          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg"
                        >
                          Complete (Cash)
                        </button>
                        <button
                          onClick={() =>
                            handleCompleteOrder(order._id, "online")
                          }
                          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg"
                        >
                          Complete (Online)
                        </button>
                        <button
                          onClick={() => handleAddItemsToOrder(order._id)}
                          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg"
                        >
                          Add Items
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Order Form Modal */}
      {showOrderForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div className="p-4 md:p-6 border-b">
              <h2 className="text-xl font-bold">Complete Your Order</h2>
            </div>
            <div className="p-4 md:p-6">
              <div className="space-y-4">
                <InputField
                  type="text"
                  label={"Waiter Name"}
                  name={"name"}
                  value={user.name}
                  disabled={true}

                />
                <InputField
                  type="tel"
                  label={"Phone Number"}
                  name={"phoneNumber"}
                  value={orderDetails.phoneNumber}
                  onChange={handleOrderDetailFormChange}
                  error={errors.phoneNumber}
                  maxLength={10}
                  disabled={true}

                />

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Table Number
                  </label>
                  <select
                    name="tableNumber"
                    value={orderDetails.tableNumber}
                    onChange={handleOrderDetailFormChange}
                    className={`w-full h-10 rounded-lg border ${errors.tableNumber ? "border-red-500" : "border-gray-200"
                      } p-2`}
                  >
                    <option value="">Select table</option>
                    {tables?.map(
                      (table) =>
                        table.status === "available" && (
                          <option key={table._id} value={table.tableNumber}>
                            {table.tableNumber}
                          </option>
                        )
                    )}
                  </select>
                  {errors.tableNumber && (
                    <p className="text-sm text-red-500">{errors.tableNumber}</p>
                  )}
                </div>
                <div className="border-t pt-4">
                  <div className="flex justify-between mb-2">
                    <span>Subtotal</span>
                    <span>${calculateTotal().subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span>GST (18%)</span>
                    <span>${calculateTotal().gst.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between font-bold">
                    <span>Total Amount</span>
                    <span>${calculateTotal().total.toFixed(2)}</span>
                  </div>
                </div>
                <div className="flex gap-2 mt-4">
                  <button
                    onClick={() => setShowOrderForm(false)}
                    className="w-full py-3 px-4 border rounded-lg hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handlePlaceOrder}
                    className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg"
                  >
                    Place Order
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WaiterMenu;
