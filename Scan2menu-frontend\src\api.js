import axios from "axios"

export const axiosInstance = axios.create({
    baseURL: import.meta.env.VITE_ENV === 'development' ? import.meta.env.VITE_APP_LOCAL_API_URL : import.meta.env.VITE_APP_API_URL,
    withCredentials: true,
})

// user routes
export const postLoginData = async (data) => {
    const response = await axiosInstance.post("/users/login", data)
    return response
}
export const postSignupData = async (data) => {
    const response = await axiosInstance.post("/users/signup", data)
    return response
}

export const getSingleUser = async (id) => {
    const response = await axiosInstance.get(`/users/${id}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}
// Google Authentication
export const googleAuth = async (token) => {
    const response = await axios.post(`${axiosInstance.defaults.baseURL}/users/auth/google`, { token });
    return response;
};

export const postLogoutData = async () => {
    const response = await axiosInstance.post("/users/logout", {}, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
        }
    });
    return response;
}

export const deleteStaffAccount = async (id) => {
    const response = await axiosInstance.delete(`/users/${id}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}
export const getAllRestaurantStaff = async (id) => {

    const response = await axiosInstance.get(`/users/restaurant/staf/${id}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}
// dashboard route

export const getDashboardData = async (id) => {
    const response = await axiosInstance.get(`/restaurant/dashboard/${id}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}

// restaurant routes

export const postRestaurantData = async (data) => {
    const response = await axiosInstance.post('/restaurant', data, {
        headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${localStorage.getItem("token")}`,
        }

    })
    return response
}

export const updateRestaurantData = async (data, id) => {
    const response = await axiosInstance.put(`/restaurant/${id}`, data, {
        headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${localStorage.getItem("token")}`,
        }
    })
    return response
}


// category routes
export const addCategory = async (data) => {
    const response = await axiosInstance.post('/category', data, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`
        }
    });
    return response;
}

export const getAllCategories = async (id) => {
    const response = await axiosInstance.get(`/category/${id}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}
export const getSingleCategory = async (id) => { //category id
    const response = await axiosInstance.get(`/category/c/${id}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}
export const updateCategory = async (id, data) => { //category id
    const response = await axiosInstance.put(`/category/${id}`, data, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}
export const deleteCategory = async (id) => { //category id
    const response = await axiosInstance.delete(`/category/${id}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}

//subcategory routes
export const addSubcategory = async (data) => {
    const response = await axiosInstance.post('/subcategory', data, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`
        }
    });
    return response;
}

export const getSingleSubcategory = async (id) => { //subcategory id
    const response = await axiosInstance.get(`/subcategory/s/${id}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}

export const getAllSubcategoriesByCategory = async (categoryId) => {
    const response = await axiosInstance.get(`/subcategory/${categoryId}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}

export const updateSubcategory = async (id, data) => {
    const response = await axiosInstance.put(`/subcategory/${id}`, data, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}

export const deleteSubcategory = async (id) => {
    const response = await axiosInstance.delete(`/subcategory/${id}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}

// item routes
export const addItem = async (data) => {
    const response = await axiosInstance.post('/item', data, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`
        }
    });
    return response;
}

export const getSingleItem = async (id) => { //item id
    const response = await axiosInstance.get(`/item/${id}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}


export const updateItem = async (id, data) => {
    const response = await axiosInstance.put(`/item/${id}`, data, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}

export const deleteItem = async (id) => {
    const response = await axiosInstance.delete(`/item/${id}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}

export const addExtra = async (id, extraData) => {
    const response = await axiosInstance.put(`/item/add-extra/${id}`, extraData, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}

export const removeExtra = async (itemId, extraId) => {
    const response = await axiosInstance.put(`/item/remove-extra/${itemId}`, { extraId }, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}

//user
export const updateUser = async (userId, updateData) => {
    const response = await axiosInstance.put(`/users/${userId}`, updateData, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}

// update staff checkin/out
export const updateStaffCheckInOut = async (staffId, data) => {
    const response = await axiosInstance.post(`/users/restaurant/staf/checkinout/${staffId}`, data, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}
// Umenu routes

export const getwholeMenu = async (slug) => {
    const response = await axiosInstance.get(`/u/menu/${slug}`);
    return response;
};

// order routes

export const createOrder = async (data) => {
    const response = await axiosInstance.post('/u/order', data);
    return response;
}

export const createOrderByWaiter = async (data) => {
    const response = await axiosInstance.post('/order/waiter', data, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}


// Order routes
export const getAllOrders = async (restaurantId, page = 1, limit = 10) => {
    const url = `/order/${restaurantId}?page=${page}&limit=${limit}`;
    const response = await axiosInstance.get(url, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}

export const getOrdersByFilter = async (restaurantId, date, page = 1, limit = 10) => {
    const response = await axiosInstance.get(`/order/filter/${restaurantId}?date=${date}&page=${page}&limit=${limit}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}

export const deleteOrder = async (orderId) => {
    const response = await axiosInstance.delete(`/order/d/${orderId}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}

export const updateOrderStatus = async (orderId, status) => {
    const response = await axiosInstance.put(`/order/${orderId}`, { status }, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}

export const getAllWaiterOrders = async (awaiterId) => {
    const response = await axiosInstance.get(`/order/waiter/${awaiterId}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}

// Table routes
export const getAllTables = async (restaurantSlug) => {
    const response = await axiosInstance.get(`/tables/${restaurantSlug}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`
        }
    });
    return response;
}


export const getQRCodeSettings = async (restaurantId) => {
    const response = await axiosInstance.get(`/qrcode/${restaurantId}`, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
    });
    return response;
};

export const updateQRCodeSettings = async (restaurantId, settings) => {
    const response = await axiosInstance.put(`/qrcode/${restaurantId}`, settings, {
        headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
    });
    return response;
};

// Waiter routes

// Payment routes
export const createPaymentOrder = async (amount) => {
    const response = await axiosInstance.post('/payment/create', { amount });
    return response;
};

export const verifyPayment = async (paymentData) => {
    const response = await axiosInstance.post('/payment/verify', paymentData);
    return response;
};
