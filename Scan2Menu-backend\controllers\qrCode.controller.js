const QRCode = require('../models/qrCode.model');
const Restaurant = require('../models/restaurant.model');

exports.getQRCodeSettings = async (req, res, next) => {
  try {
    const { restaurantId } = req.params;
    const qrCode = await QRCode.findOne({ restaurantId });
    
    if (!qrCode) {
      return res.status(404).json({
        success: false,
        message: 'QR code settings not found'
      });
    }

    res.status(200).json({
      success: true,
      data: qrCode
    });
  } catch (error) {
    next(error);
  }
};

exports.updateQRCodeSettings = async (req, res, next) => {
  try {
    const { restaurantId } = req.params;
    const settings = req.body;

    const qrCode = await QRCode.findOneAndUpdate(
      { restaurantId },
      { qrCodeSettings: settings },
      { new: true, upsert: true }
    );

    res.status(200).json({
      success: true,
      data: qrCode
    });
  } catch (error) {
    next(error);
  }
};