const mongoose = require('mongoose');
const Restaurant = require("./restaurant.model");
const crypto = require('crypto');
const { required, optional } = require('joi');

// Define the schema for the User model
const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  username: {
    type: String,
    required: true,
    unique: true,
  },
  email: {
    type: String,
    required: true,
    unique: true,
  },
  phone: {
    type: String,
    required: function() {
      return !this.googleId; // Phone is required only for local auth
    },
    default: undefined, // Use undefined instead of null
    validate: {
      validator: function(v) {
        // Allow undefined for Google auth users
        return v === undefined || /^\d{10}$/.test(v);
      },
      message: 'Phone number must be 10 digits'
    }
  },
  password: {
    type: String,
    required: function() {
      return !this.googleId; // Password is required only for local auth
    },
    minlength: 6,
  },
  googleId: {
    type: String,
    unique: true,
    sparse: true,
  },
  picture: {
    type: String,
    default: '',
  },
  role: {
    type: String,
    required: true,
    enum: ['admin', 'chef', 'waiter', 'user'],
    default: 'admin'
  },
  resetPasswordToken: { type: String, default: null },
  resetPasswordExpires: { type: Date, default: null },
}, { timestamps: true });

// Create indexes
userSchema.index({ phone: 1 }, { unique: true, sparse: true, background: true });

// Pre-remove middleware to delete related restaurants
userSchema.pre('remove', async function (next) {
  try {
    await Restaurant.deleteMany({ userId: this._id }); // Delete all restaurants linked to this user
    next();
  } catch (err) {
    next(err);
  }
});

// Genereat the reset token
userSchema.methods.generateResetToken = function () {
  const token = crypto.randomBytes(32).toString('hex');
  this.resetPasswordToken = crypto.createHash('sha256').update(token).digest('hex');
  this.resetPasswordExpires = Date.now() + 3600000; // Token valid for 1 hour
  return token;
};
// Create the model using the schema
const User = mongoose.model('User', userSchema);

module.exports = User;