import React, { useContext, useEffect, useState } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import { postLogoutData } from '../../api';
import { UserDataContext } from '../../context/UserContext';
import { RestaurantDataContext } from '../../context/RestaurantContext';
import {
  RiDashboardLine,
  RiRestaurantLine,
  RiFileListLine,
  RiUserSettingsLine,
  RiQrCodeLine,
  RiMenuLine,
  RiLogoutBoxLine,
  RiSettings4Line,
  RiCloseLine
} from 'react-icons/ri';
import { toast } from 'react-hot-toast';


const LeftSideBar = ({ onClose }) => {
  const navigate = useNavigate();
  const { setUser } = useContext(UserDataContext);
  const { restaurantData, setRestaurantData } = useContext(RestaurantDataContext);
  const [localRestaurantName, setLocalRestaurantName] = useState('');

  useEffect(() => {
    // Try to get data from context first
    if (restaurantData?.name) {
      setLocalRestaurantName(restaurantData.name);
    } else {
      // Fallback to localStorage
      const storedData = localStorage.getItem('restaurantData');
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        setLocalRestaurantName(parsedData?.name || '');
        setRestaurantData(parsedData);
      }
    }
  }, [restaurantData, setRestaurantData]);

  const handleLogout = async () => {
    try {
      let response = await postLogoutData();
      if (response.data.success) {
        setUser(null);
        setRestaurantData(null);
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('restaurantData');
        toast.success('Logged out successfully');
        navigate('/login');
      }
    } catch (error) {

      toast.error(error?.response?.data?.message || error.message);
    }
  };

  return (
    <div className="w-64 fixed top-16 left-0 bottom-0 h-full bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-gray-300 flex flex-col shadow-2xl backdrop-blur-sm border-r border-gray-700/50">
      {/* Mobile Close Button */}
      {onClose && (
        <button
          onClick={onClose}
          className="sm:hidden absolute top-4 right-4 p-2 hover:bg-gray-700/50 rounded-full"
        >
          <RiCloseLine className="text-xl" />
        </button>
      )}

      <div className="flex flex-col space-y-3 p-6  h-screen overflow-auto scrollbar-thinner">
        <div className="mb-6 text-center">
          <h2 className="text-xl font-bold text-white mb-2">
            {localRestaurantName || 'Loading...'}
          </h2>
          <div className="h-1 w-16 bg-gradient-to-r from-[#E28E66] to-[#F2A44F] mx-auto rounded-full"></div>
        </div>


        <NavLink
          to="/"
          className={({ isActive }) =>
            `flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-105 ${isActive
              ? 'bg-gradient-to-r from-[#E28E66] to-[#F2A44F] text-white shadow-lg shadow-[#E28E66]/25'
              : 'hover:bg-gray-700/50 hover:text-white hover:shadow-md'
            }`
          }
        >
          <RiDashboardLine className={`text-xl transition-transform duration-300 group-hover:rotate-12`} />
          <span className="font-medium">Dashboard</span>
        </NavLink>

        <NavLink
          to="/restaurant"
          className={({ isActive }) =>
            `flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-105 ${isActive
              ? 'bg-gradient-to-r from-[#E28E66] to-[#F2A44F] text-white shadow-lg shadow-[#E28E66]/25'
              : 'hover:bg-gray-700/50 hover:text-white hover:shadow-md'
            }`
          }
        >
          <RiRestaurantLine className="text-xl" />
          <span className="font-medium">Restaurant</span>
        </NavLink>

        <NavLink
          to="/order"
          className={({ isActive }) =>
            `flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-105 ${isActive
              ? 'bg-gradient-to-r from-[#E28E66] to-[#F2A44F] text-white shadow-lg shadow-[#E28E66]/25'
              : 'hover:bg-gray-700/50 hover:text-white hover:shadow-md'
            }`
          }
        >
          <RiFileListLine className="text-xl" />
          <span className="font-medium">Order</span>
        </NavLink>

        <NavLink
          to="/edit-profile"
          className={({ isActive }) =>
            `flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-105 ${isActive
              ? 'bg-gradient-to-r from-[#E28E66] to-[#F2A44F] text-white shadow-lg shadow-[#E28E66]/25'
              : 'hover:bg-gray-700/50 hover:text-white hover:shadow-md'
            }`
          }
        >
          <RiUserSettingsLine className="text-xl" />
          <span className="font-medium">Edit Profile</span>
        </NavLink>

        <NavLink
          to="/qr-code"
          className={({ isActive }) =>
            `flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-105 ${isActive
              ? 'bg-gradient-to-r from-[#E28E66] to-[#F2A44F] text-white shadow-lg shadow-[#E28E66]/25'
              : 'hover:bg-gray-700/50 hover:text-white hover:shadow-md'
            }`
          }
        >
          <RiQrCodeLine className="text-xl" />
          <span className="font-medium">QR Code</span>
        </NavLink>

        <NavLink
          to="/menu"
          className={({ isActive }) =>
            `flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-105 ${isActive
              ? 'bg-gradient-to-r from-[#E28E66] to-[#F2A44F] text-white shadow-lg shadow-[#E28E66]/25'
              : 'hover:bg-gray-700/50 hover:text-white hover:shadow-md'
            }`
          }
        >
          <RiMenuLine className="text-xl" />
          <span className="font-medium">Menu</span>
        </NavLink>

        <div className="my-4 border-t border-gray-700/50"></div>

        <button
          onClick={handleLogout}
          className="flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-105
            hover:bg-gradient-to-r from-red-500 to-pink-500 hover:text-white hover:shadow-lg 
            hover:shadow-red-500/25 group"
        >
          <RiLogoutBoxLine className="text-xl group-hover:rotate-12 transition-transform duration-300" />
          <span className="font-medium">Logout</span>
        </button>

        <NavLink
          to="/settings"
          className={({ isActive }) =>
            `flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ease-in-out transform hover:scale-105 ${isActive
              ? 'bg-gradient-to-r from-[#E28E66] to-[#F2A44F] text-white shadow-lg shadow-[#E28E66]/25'
              : 'hover:bg-gray-700/50 hover:text-white hover:shadow-md'
            }`
          }
        >
          <RiSettings4Line className="text-xl" />
          <span className="font-medium">Settings</span>
        </NavLink>
      </div>

      {/* <div className="mt-auto border-t border-gray-700/50 p-6 space-y-4">
        <div className="flex items-center space-x-3 px-4 py-3">
          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-[#E28E66] to-[#F2A44F] flex items-center justify-center text-white font-bold">
            JD
          </div>
          <div className="flex flex-col">
            <span className="text-sm font-medium text-white">John Doe</span>
            <span className="text-xs text-gray-400"><EMAIL></span>
          </div>
        </div>
      </div> */}
    </div>
  );
};

export default LeftSideBar;

