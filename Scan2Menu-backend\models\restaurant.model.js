const mongoose = require('mongoose');
const User = require("./user.model");

const restaurantSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    match: /^[a-zA-Z0-9-]+$/,
    lowercase: true,
    maxlength: 100,
  },
  subTitle: {
    type: String,
    maxlength: 150,
    trim: true,
  },
  timing: {
    type: String,
    required: true,
    maxlength: 100,
  },
  description: {
    type: String,
    maxlength: 500,
    trim: true,
  },
  location: {
    type: String,
    required: true,
    maxlength: 200,
    trim: true,
  },
  image: {
    type: String,
    required: true,
  },
  coverImage: {
    type: String,
    required: true,
  },
  allowOnTableOrder: {
    type: String,
    enum: ['yes', 'no'],
    default: 'no',
  },
  allowTAX: {
    type: String,
    enum: ['yes', 'no'],
    default: 'no',
  },
  taxPercentage: {
    type: Number,
    min: 0,
    max: 100,
    required: function () {
      return this.allowTAX === 'yes';
    },
  },
  gstID: {
    type: String,
    match: /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  numberOfTables: {
    type: Number,
    required: true,
    min: 1,
    max: 100,
  },
}, { timestamps: true });

module.exports = mongoose.model('Restaurant', restaurantSchema);
