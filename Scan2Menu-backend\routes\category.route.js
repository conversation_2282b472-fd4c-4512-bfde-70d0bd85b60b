const express = require('express');
const router = express.Router();
const categoryController = require('../controllers/category.controller');


router.post('/',categoryController.addcategory);
router.get('/c/:id',categoryController.getsinglecategory);//get single catgeory Byid
router.get('/:restaurantId',categoryController.getallcatgory);//get all category of restaurant by restaurant id
router.put('/:id',categoryController.updatecategory);
router.delete('/:id',categoryController.deletecategory);
// Reorder categories for drag and drop
router.put('/reorder/:restaurantId', categoryController.reorderCategories);


module.exports = router;