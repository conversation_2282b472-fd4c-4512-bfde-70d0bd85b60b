const Restaurant = require('../models/restaurant.model');
const Category = require('../models/category.model');


//get whole menu
exports.getwholeMenu = async (req, res, next) => {
  try {
    const { slug } = req.params;

    // Find restaurant by slug
    const restaurant = await Restaurant.findOne({ slug });

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        message: 'Restaurant not found'
      });
    }

    // Fetch categories with populated subcategories and items for specific restaurant
    const categories = await Category.find({ restaurantId: restaurant._id })
      .populate({
        path: 'subcategories',
        populate: {
          path: 'items',
          populate: 'extras'
        }
      })
      .populate({
        path: 'items',
        populate: 'extras'
      })
      .lean();

    if (!categories.length) {
      return res.status(404).json({
        success: false,
        message: 'No menu found for this restaurant'
      });
    }

    // Count veg and non-veg items
    let vegCount = 0;
    let nonVegCount = 0;

    // Count from direct category items
    categories.forEach(category => {
      if (category.items) {
        category.items.forEach(item => {
          if (item.type === "veg") vegCount++;
          else nonVegCount++;
        });
      }
      // Count from subcategory items
      if (category.subcategories) {
        category.subcategories.forEach(subcategory => {
          if (subcategory.items) {
            subcategory.items.forEach(item => {
              if (item.type === "veg") vegCount++;
              else nonVegCount++;
            });
          }
        });
      }
    });

    res.status(200).json({
      success: true,
      message: 'Menu data retrieved successfully',
      data: categories,
      restaurant: {
        name: restaurant.name,
        image: restaurant.image,
        id: restaurant._id
      },
      itemCounts: {
        vegetarian: vegCount,
        nonVegetarian: nonVegCount,
        total: vegCount + nonVegCount
      }
    });
  } catch (error) {
    next(error);
  }
}
