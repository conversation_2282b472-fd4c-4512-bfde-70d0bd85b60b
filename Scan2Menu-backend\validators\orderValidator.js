const Joi = require('joi');
const mongoose = require('mongoose');

const orderValidationSchema = Joi.object({
  name: Joi.string().max(100).required().trim().messages({
    'any.required': '"name" is a required field',
  }),

  tableNumber: Joi.number().min(1).required().messages({
    'any.required': '"tableNumber" is a required field',
  }),

  phoneNumber: Joi.string()
    .pattern(/^[0-9]{10}$/)
    .required()
    .messages({
      'any.required': '"phoneNumber" is a required field',
    }),

  restaurantId: Joi.string().required().messages({
    'any.required': '"restaurantId" is a required field',
  }),
  subtotal: Joi.number().min(0).required().messages({
    'any.required': '"subtotal" is a required field',
  }),

  gst: Joi.number().min(0).required().messages({
    'any.required': '"gst" is a required field',
  }),

  total: Joi.number().min(0).required().messages({
    'any.required': '"total" is a required field',
  }),
  orderby: Joi.string().messages({
    'any.required': '"orderby" is a required field',
  }),

  items: Joi.array()
    .items(
      Joi.object({
        itemId: Joi.string()
          .custom((value, helpers) => {
            if (!mongoose.Types.ObjectId.isValid(value)) {
              return helpers.error('any.invalid');
            }
            return value;
          }, 'ObjectId validation')
          .required()
          .messages({
            'any.required': '"itemId" is required',
          }),

        quantity: Joi.number().min(1).required().messages({
          'any.required': '"quantity" is required',
        }),
        selectedExtras: Joi.array()
          .items(
            Joi.string()
              .custom((value, helpers) => {
                if (!mongoose.Types.ObjectId.isValid(value)) {
                  return helpers.error('any.invalid');
                }
                return value;
              }, 'ObjectId validation')
          )
          .optional()
          .messages({
            'array.base': '"selectedExtras" must be an array of valid ObjectIds',
          }),
      })
    )
    .min(1)
    .required()
    .messages({
      'any.required': '"items" is required',
    }),

  status: Joi.string()
    .valid('pending', 'preparing', 'served', 'completed', 'cancelled')
    .default('pending'),
});

module.exports = {
  orderValidationSchema,
};
