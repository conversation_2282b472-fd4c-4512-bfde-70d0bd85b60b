// const Joi = require('joi');

// // Joi schema for validating user data
// const userValidationSchema = Joi.object({
//   name: Joi.string()
//     .min(2)
//     .max(50)
//     .required()
//     .trim()
//     .messages({
//       'string.base': '"name" should be a type of "string"',
//       'string.empty': '"name" cannot be empty',
//       'string.min': '"name" should have a minimum length of 2 characters',
//       'string.max': '"name" should have a maximum length of 50 characters',
//       'any.required': '"name" is a required field',
//     }),

//   username: Joi.string()
//     .alphanum()
//     .min(3)
//     .max(30)
//     .required()
//     .trim()
//     .messages({
//       'string.base': '"username" should be a type of "string"',
//       'string.empty': '"username" cannot be empty',
//       'string.alphanum': '"username" should only contain alphanumeric characters',
//       'string.min': '"username" should have a minimum length of 3 characters',
//       'string.max': '"username" should have a maximum length of 30 characters',
//       'any.required': '"username" is a required field',
//     }),

//   email: Joi.string()
//     .email({ minDomainSegments: 2 })
//     .required()
//     .trim()
//     .messages({
//       'string.base': '"email" should be a type of "string"',
//       'string.empty': '"email" cannot be empty',
//       'string.email': '"email" must be a valid email address',
//       'any.required': '"email" is a required field',
//     }),

//   phone: Joi.string()
//     .pattern(/^[0-9]{10}$/)
//     .required()
//     .trim()
//     .messages({
//       'string.base': '"phone" should be a type of "string"',
//       'string.empty': '"phone" cannot be empty',
//       'string.pattern.base': '"phone" must be a valid 10-digit phone number',
//       'any.required': '"phone" is a required field',
//     }),
//   password: Joi.string()
//     .min(6)
//     .max(30)
//     .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{6,}$/)
//     .required()
//     .trim()
//     .messages({
//       'string.base': '"password" should be a type of "string"',
//       'string.empty': '"password" cannot be empty',
//       'string.min': '"password" should have a minimum length of 6 characters',
//       'string.max': '"password" should have a maximum length of 30 characters',
//       'string.pattern.base': '"password" must contain at least one uppercase letter, one lowercase letter, one number and one special character',
//       'any.required': '"password" is a required field',
//     }),
//   resetPasswordToken: Joi.string().optional(),
//   resetPasswordExpires: Joi.string().optional(),
//   role: Joi.string()
//     .valid('user', 'chef', 'waiter', 'admin')
//     .default('admin')
//     .optional()
//     .messages({
//       'string.base': '"role" should be a type of "string"',
//       'string.empty': '"role" cannot be empty',
//       'any.only': '"role" must be one of [user, staff, admin]',
//       'any.required': '"role" is a required field'
//     })
// });

// module.exports = userValidationSchema;

const Joi = require('joi');

// Joi schema for validating user data
const userValidationSchema = Joi.object({
  name: Joi.string()
    .min(2)
    .max(50)
    .required()
    .trim()
    .messages({
      'string.base': '"name" should be a type of "string"',
      'string.empty': '"name" cannot be empty',
      'string.min': '"name" should have a minimum length of 2 characters',
      'string.max': '"name" should have a maximum length of 50 characters',
      'any.required': '"name" is a required field',
    }),

  username: Joi.string()
    .alphanum()
    .min(3)
    .max(30)
    .required()
    .trim()
    .messages({
      'string.base': '"username" should be a type of "string"',
      'string.empty': '"username" cannot be empty',
      'string.alphanum': '"username" should only contain alphanumeric characters',
      'string.min': '"username" should have a minimum length of 3 characters',
      'string.max': '"username" should have a maximum length of 30 characters',
      'any.required': '"username" is a required field',
    }),

  email: Joi.string()
    .email({ minDomainSegments: 2 })
    .required()
    .trim()
    .messages({
      'string.base': '"email" should be a type of "string"',
      'string.empty': '"email" cannot be empty',
      'string.email': '"email" must be a valid email address',
      'any.required': '"email" is a required field',
    }),

  phone: Joi.string()
    .pattern(/^[0-9]{10}$/)
    .required()
    .trim()
    .messages({
      'string.base': '"phone" should be a type of "string"',
      'string.empty': '"phone" cannot be empty',
      'string.pattern.base': '"phone" must be a valid 10-digit phone number',
      'any.required': '"phone" is a required field',
    }),

  password: Joi.string()
    .min(6)
    .max(30)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{6,}$/)
    .required()
    .trim()
    .messages({
      'string.base': '"password" should be a type of "string"',
      'string.empty': '"password" cannot be empty',
      'string.min': '"password" should have a minimum length of 6 characters',
      'string.max': '"password" should have a maximum length of 30 characters',
      'string.pattern.base': '"password" must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      'any.required': '"password" is a required field',
    }),

  role: Joi.string()
    .valid('user', 'chef', 'waiter', 'admin')
    .required()
    .messages({
      'string.base': '"role" should be a type of "string"',
      'string.empty': '"role" cannot be empty',
      'any.only': '"role" must be one of [user, chef, waiter, admin]"',
      'any.required': '"role" is a required field',
    }),

  restaurant_detail: Joi.object({
    id: Joi.string()
      .required()
      .messages({
        'string.base': '"restaurant_detail.id" should be a type of "string"',
        'string.empty': '"restaurant_detail.id" cannot be empty',
        'any.required': '"restaurant_detail.id" is required when role is chef or waiter',
      }),
    slug: Joi.string()
      .required()
      .messages({
        'string.base': '"restaurant_detail.slug" should be a type of "string"',
        'string.empty': '"restaurant_detail.slug" cannot be empty',
        'any.required': '"restaurant_detail.slug" is required when role is chef or waiter',
      }),
  })
    .when('role', {
      is: Joi.valid('chef', 'waiter'),
      then: Joi.required(),
      otherwise: Joi.forbidden(), // Restrict restaurant details for other roles
    }),

  resetPasswordToken: Joi.string().optional(),
  resetPasswordExpires: Joi.string().optional(),
  shift: Joi.string().optional(),
  working_hours: Joi.number().optional(),

});

module.exports = userValidationSchema;