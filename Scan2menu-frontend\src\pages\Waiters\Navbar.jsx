import React, { useState, useContext } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Menu, LogOut, User, List, Home } from 'lucide-react';
import { postLogoutData } from '../../api';
import { toast } from 'react-hot-toast';
import { UserDataContext } from '../../context/UserContext';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const { setUser } = useContext(UserDataContext);
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      let response = await postLogoutData();
      if (response.data.success) {
        setUser(null);
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        toast.success('Logged out successfully');
        navigate('/login');
      }
    } catch (error) {
      toast.error(error?.response?.data?.message || error?.message);
    }
  };

  return (
    <nav className="bg-white shadow-md">
      <div className="max-w-7xl mx-auto px-4">
        {/* Desktop Navigation */}
        <div className="hidden md:flex justify-between h-16 items-center">
          <div className="flex items-center">
            <span className="text-xl font-bold text-blue-600">Scan2Menu</span>
          </div>
          <div className="flex items-center space-x-4">
            <Link
              to="/staff/dashboard"
              className={`flex items-center space-x-1 px-3 py-2 rounded-md ${location.pathname === "/staff" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
            >
              <Home className="h-5 w-5" />
              <span>Home</span>
            </Link>
            <Link
              to="/staff/menu"
              className={`flex items-center space-x-1 px-3 py-2 rounded-md ${location.pathname === "/waitermenu" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
            >
              <List className="h-5 w-5" />
              <span>Menu</span>
            </Link>
            <Link
              to="/staff/profile"
              className={`flex items-center space-x-1 px-3 py-2 rounded-md ${location.pathname === "/profile" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
            >
              <User className="h-5 w-5" />
              <span>Profile</span>
            </Link>
            {/* ✅ Fixed Logout Button - Removed duplicate <button> */}
            <button
              onClick={handleLogout}
              className="flex items-center space-x-1 px-3 py-2 text-red-600 rounded-md hover:bg-red-50"
            >
              <LogOut className="h-5 w-5" />
              <span>Logout</span>
            </button>
          </div>
        </div>

        {/* Mobile Navigation Header */}
        <div className="flex md:hidden justify-between h-16 items-center">
          <div className="flex items-center space-x-2">
            <span className="text-xl font-bold text-blue-600">Scan2Menu</span>
            {/* ✅ Fixed Logout Button - Removed duplicate <button> */}
            <button
              onClick={handleLogout}
              className="flex items-center space-x-1 text-red-600 px-2 py-1 rounded-md hover:bg-red-50"
            >
              <LogOut className="h-5 w-5" />
              <span>Logout</span>
            </button>
          </div>
          <button className="p-2" onClick={() => setIsMenuOpen(!isMenuOpen)}>
            <Menu className="h-6 w-6" />
          </button>
        </div>
      </div>

      {/* Mobile Dropdown Navigation */}
      {isMenuOpen && (
        <div className="md:hidden border-t border-gray-200">
          <div className="px-2 py-3 space-y-1">
            <Link
              to="/staff/dashboard"
              className={`flex items-center space-x-2 px-3 py-2 rounded-md ${location.pathname === "/staff" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
            >
              <Home className="h-5 w-5" />
              <span>Home</span>
            </Link>
            <Link
              to="/staff/menu"
              className={`flex items-center space-x-2 px-3 py-2 rounded-md ${location.pathname === "/waitermenu" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
            >
              <List className="h-5 w-5" />
              <span>Menu</span>
            </Link>
            <Link
              to="/staff/profile"
              className={`flex items-center space-x-2 px-3 py-2 rounded-md ${location.pathname === "/profile" ? "bg-blue-100 text-blue-700" : "hover:bg-gray-100"
                }`}
            >
              <User className="h-5 w-5" />
              <span>Profile</span>
            </Link>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;